from modelscope.hub.snapshot_download import snapshot_download
import os

model_dir = "./model"
model_id = 'deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B'  # 替换为目标模型ID
try:
    model_dir = snapshot_download(model_id)  # 返回本地路径
    print(f"模型下载到: {model_dir}")
    
    # 检查必备文件是否存在
    required_files = [
        'config.json',           # 配置文件
        'model.safetensors',     # 模型权重（或 pytorch_model.bin）
        'tokenizer.json',        # 分词器配置
        'special_tokens_map.json'
    ]
    missing = [f for f in required_files if not os.path.exists(os.path.join(model_dir, f))]
    
    if not missing:
        print("✅ 基础验证成功：关键文件完整")
    else:
        print(f"❌ 缺失文件：{missing}")
except Exception as e:
    print(f"下载失败：{str(e)}")