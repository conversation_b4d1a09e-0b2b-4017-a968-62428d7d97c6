import argparse
import os
import time
from datetime import datetime, timedelta
import uuid
import json
import sys

STATE_FILE = "llm_job_state.json"

class PipelineState:
    def __init__(self, model_name=None, job_id=None):
        if job_id:
            # 从现有状态加载
            self.load_state()
        else:
            # 创建新状态
            self.id = f"import-model-job-{str(uuid.uuid4())[:8]}"
            self.model_name = model_name
            self.status = "started"
            self.created_at = datetime.utcnow()
            self.started_at = None
            self.updated_at = self.created_at
            self.finished_at = None
            self.generated_image = None
            self.error = None
            
            self.steps = [
                {"id": 0, "name": "Download Model", "status": "pending", 
                 "started_at": None, "finished_at": None, "duration": 0, 
                 "exit_code": None, "error_info": None},
                {"id": 1, "name": "Build Images", "status": "pending", 
                 "started_at": None, "finished_at": None, "duration": 0, 
                 "exit_code": None, "error_info": None},
                {"id": 2, "name": "Upload Images", "status": "pending", 
                 "started_at": None, "finished_at": None, "duration": 0, 
                 "exit_code": None, "error_info": None}
            ]
            self.save_state()
    
    def update_state(self):
        self.updated_at = datetime.utcnow()
        
        # 计算持续时间
        if self.started_at:
            self.duration = int((self.updated_at - self.started_at).total_seconds())
        
        # 计算进度
        completed = sum(1 for step in self.steps if step["status"] == "success")
        total = len(self.steps)
        percentage = int((completed / total) * 100) if total > 0 else 0
        
        self.progress = {
            "completed_steps": completed,
            "total_steps": total,
            "percentage": percentage
        }
        
        # 更新管道状态
        if all(step["status"] == "success" for step in self.steps):
            self.status = "success"
            self.finished_at = datetime.utcnow()
        elif any(step["status"] == "failure" for step in self.steps):
            self.status = "failure"
            self.finished_at = datetime.utcnow()
        elif any(step["status"] in ["running", "success"] for step in self.steps):
            self.status = "running"
        
        self.save_state()
    
    def to_dict(self):
        return {
            "pipeline": {
                "id": self.id,
                "name": "import-model-job",
                "status": self.status,
                "created_at": self.created_at.strftime("%Y-%m-%dT%H:%M:%SZ"),
                "started_at": self.started_at.strftime("%Y-%m-%dT%H:%M:%SZ") if self.started_at else None,
                "updated_at": self.updated_at.strftime("%Y-%m-%dT%H:%M:%SZ"),
                "finished_at": self.finished_at.strftime("%Y-%m-%dT%H:%M:%SZ") if self.finished_at else None,
                "duration": self.duration,
                "error": self.error,
                "generated_image": self.generated_image,
                "progress": self.progress,
                "steps": self.steps
            }
        }
    
    def save_state(self):
        """将状态保存到JSON文件"""
        with open(STATE_FILE, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)
    
    def load_state(self):
        """从JSON文件加载状态"""
        try:
            with open(STATE_FILE, 'r') as f:
                data = json.load(f)['pipeline']
                
            self.id = data['id']
            self.status = data['status']
            self.created_at = datetime.strptime(data['created_at'], "%Y-%m-%dT%H:%M:%SZ")
            self.started_at = datetime.strptime(data['started_at'], "%Y-%m-%dT%H:%M:%SZ") if data['started_at'] else None
            self.updated_at = datetime.strptime(data['updated_at'], "%Y-%m-%dT%H:%M:%SZ")
            self.finished_at = datetime.strptime(data['finished_at'], "%Y-%m-%dT%H:%M:%SZ") if data['finished_at'] else None
            self.duration = data['duration']
            self.error = data['error']
            self.generated_image = data['generated_image']
            self.progress = data['progress']
            self.steps = data['steps']
            
        except (FileNotFoundError, json.JSONDecodeError, KeyError):
            print(f"Error: State file {STATE_FILE} is missing or invalid")
            sys.exit(1)

def main(): 
    parser = argparse.ArgumentParser(description="import tools")

    parser.add_argument("--state", action='store_true')
    # parser.add_argument("--import", action='store_true')
    parser.add_argument("--import", default="deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B", 
                        help="hugging face model")

    args = parser.parse_args()

    if args.state:
        if os.path.exists("llm_job_state.json"):
            print("State")
        else:
            print("Not run!")
        return
    


if __name__ == "__main__":
    main()
