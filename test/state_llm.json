{"pipeline": {"id": "import-model-job-123456", "name": "import-model-job", "status": "running", "created_at": "2025-06-16T10:00:00Z", "started_at": "2025-06-16T10:01:00Z", "updated_at": "2025-06-16T10:15:30Z", "finished_at": null, "duration": 870, "error": null, "generated_image": "*************/moments8/inference-serving:model-id", "progress": {"completed_steps": 2, "total_steps": 3, "percentage": 40}, "steps": [{"id": 0, "name": "Download Model", "status": "success", "started_at": "2025-06-16T10:01:00Z", "finished_at": "2025-06-16T10:02:30Z", "duration": 90, "exit_code": 0, "error_infor": "null"}, {"id": 1, "name": "Build Images", "status": "success", "started_at": "2025-06-16T10:02:30Z", "finished_at": "2025-06-16T10:08:15Z", "duration": 345, "exit_code": 0, "error_infor": "null"}, {"id": 2, "name": "Upload Images", "status": "running", "started_at": "2025-06-16T10:08:15Z", "finished_at": null, "duration": 435, "exit_code": null, "error_infor": "null"}]}}