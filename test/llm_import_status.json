{"pipeline": {"id": "import-model-job-1751881090", "name": "import-model-job", "status": "success", "created_at": "2025-07-07T09:38:10Z", "started_at": "2025-07-07T09:38:10Z", "updated_at": "2025-07-07T09:38:26Z", "finished_at": "2025-07-07T09:38:26Z", "duration": 16, "error": null, "generated_image": "*************/moments8/inference-serving:my-awesome-model", "progress": {"completed_steps": 3, "total_steps": 3, "percentage": 100}, "steps": [{"id": 0, "name": "Download Model", "status": "success", "started_at": "2025-07-07T09:38:10Z", "finished_at": "2025-07-07T09:38:15Z", "duration": 5, "exit_code": 0, "error_infor": null}, {"id": 1, "name": "Build Images", "status": "success", "started_at": "2025-07-07T09:38:15Z", "finished_at": "2025-07-07T09:38:23Z", "duration": 8, "exit_code": 0, "error_infor": null}, {"id": 2, "name": "Upload Images", "status": "failure", "started_at": null, "finished_at": null, "duration": null, "exit_code": null, "error_infor": null}]}}