# import json

# def generate_json(num):
#     # 示例的digest和size值，这里只是示例，实际使用时可能需要根据实际情况调整
#     examples = [
#         {"digest": "sha256:087d4ce3ce16c2c4b78806d70782443fe48d438c9ee70b75efc1cfbc282a5f23", "size": 7625, 
#          "features": ["birentech.com/gpu", "106C"], "annotations_key": "moments8/torin",
#          "annotations_value": "{\"request\": {\"birentech.com/gpu\": \"1\", \"cpu\": \"32\", \"memory\": \"128Gi\"}, \"limit\": {\"birentech.com/gpu\": \"1\", \"cpu\": \"32\", \"memory\": \"128Gi\"}, \"tps\": \"32\", \"nodeCapacity\": \"16\", \"step\": \"2\"}"},
#         {"digest": "sha256:90f40df815ff2a5697f8d3c864520b375b984de9974b3a6427ec81d138ef86da", "size": 2004,
#          "features": ["streamcomputing.com/npu", "STCP920"], "annotations_key": "moments8/torin",
#          "annotations_value": "{\"request\": {\"streamcomputing.com/npu\": \"4\", \"cpu\": \"32\", \"memory\": \"128Gi\"}, \"limit\": {\"streamcomputing.com/npu\": \"4\", \"cpu\": \"32\", \"memory\": \"128Gi\"}, \"tps\": \"32\", \"nodeCapacity\": \"16\", \"step\": \"0\"}"}
#     ]
    
#     manifests = []
#     for i in range(min(num, len(examples))):  # 确保不会超出examples的范围
#         example = examples[i]
#         manifest = {
#             "mediaType": "application/vnd.oci.image.manifest.v1+json",
#             "digest": example["digest"],
#             "size": example["size"],
#             "platform": {
#                 "architecture": "amd64",
#                 "os": "linux",
#                 "os.features": example["features"]
#             },
#             "annotations": {
#                 example["annotations_key"]: example["annotations_value"]
#             }
#         }
#         manifests.append(manifest)
    
#     data = {
#         "schemaVersion": 2,
#         "mediaType": "application/vnd.oci.image.index.v1+json",
#         "manifests": manifests,
#         "annotations": {
#             "moments8/torin": "{\"request\": {\"moments8.com/gpu\": \"2\", \"cpu\": \"32\", \"memory\": \"128Gi\"}, \"limit\": {\"moments8.com/gpu\": \"2\", \"cpu\": \"32\", \"memory\": \"512Gi\"}, \"tps\": 32, \"modelDescription\": \"DeepSeek-R1-Distill-Qwen-7B是通过知识蒸馏技术从DeepSeek-R1模型中提炼出来的模型。它继承了DeepSeek-R1的推理能力，在数学、编程和推理等多个领域展现出卓越的性能。\"}"
#         }
#     }

#     return json.dumps(data, indent=2)

# # 使用示例
# num = 2
# print(generate_json(num))





# import json

# def create_manifest(device_type, digest, size):
#     """
#     创建单个manifest配置
#     :param device_type: 设备类型 ('gpu' 或 'npu')
#     :param digest: 镜像摘要
#     :param size: 文件大小
#     :return: manifest字典
#     """
#     # 设备类型配置
#     device_config = {
#         'gpu': {
#             'feature': ['birentech.com/gpu', '106C'],
#             'resource': 'birentech.com/gpu',
#             'npu_count': '1'
#         },
#         'npu': {
#             'feature': ['streamcomputing.com/npu', 'STCP920'],
#             'resource': 'streamcomputing.com/npu',
#             'npu_count': '4'
#         }
#     }
#     config = device_config[device_type]
    
#     return {
#         "mediaType": "application/vnd.oci.image.manifest.v1+json",
#         "digest": digest,
#         "size": size,
#         "platform": {
#             "architecture": "amd64",
#             "os": "linux",
#             "os.features": config['feature']
#         },
#         "annotations": {
#             "moments8/torin": json.dumps({
#                 "request": {
#                     config['resource']: config['npu_count'],
#                     "cpu": "32",
#                     "memory": "128Gi"
#                 },
#                 "limit": {
#                     config['resource']: config['npu_count'],
#                     "cpu": "32",
#                     "memory": "128Gi"
#                 },
#                 "tps": "32",
#                 "nodeCapacity": "16",
#                 "step": "2" if device_type == 'gpu' else "0"
#             })
#         }
#     }

# # 主函数：构建完整JSON结构
# def generate_image_index(gpu_digest, gpu_size, npu_digest, npu_size):
#     """
#     生成完整的OCI镜像索引JSON
#     :param gpu_digest: GPU镜像摘要
#     :param gpu_size: GPU镜像大小
#     :param npu_digest: NPU镜像摘要
#     :param npu_size: NPU镜像大小
#     :return: 完整JSON字典
#     """
#     return {
#         "schemaVersion": 2,
#         "mediaType": "application/vnd.oci.image.index.v1+json",
#         "manifests": [
#             create_manifest('gpu', gpu_digest, gpu_size),
#             create_manifest('npu', npu_digest, npu_size)
#         ],
#         "annotations": {
#             "moments8/torin": json.dumps({
#                 "request": {
#                     "moments8.com/gpu": "2",
#                     "cpu": "32",
#                     "memory": "128Gi"
#                 },
#                 "limit": {
#                     "moments8.com/gpu": "2",
#                     "cpu": "32",
#                     "memory": "512Gi"
#                 },
#                 "tps": 32,
#                 "modelDescription": "DeepSeek-R1-Distill-Qwen-7B是通过知识蒸馏技术从DeepSeek-R1模型中提炼出来的模型。它继承了DeepSeek-R1的推理能力，在数学、编程和推理等多个领域展现出卓越的性能。"
#             })
#         }
#     }

# # 示例使用
# if __name__ == "__main__":
#     # 设置变量参数
#     GPU_DIGEST = "sha256:087d4ce3ce16c2c4b78806d70782443fe48d438c9ee70b75efc1cfbc282a5f23"
#     GPU_SIZE = 7625
#     NPU_DIGEST = "sha256:90f40df815ff2a5697f8d3c864520b375b984de9974b3a6427ec81d138ef86da"
#     NPU_SIZE = 2004
    
#     # 生成JSON数据
#     image_index = generate_image_index(GPU_DIGEST, GPU_SIZE, NPU_DIGEST, NPU_SIZE)
    
#     # 写入文件（带格式缩进）
#     with open('image_index.json', 'w') as f:
#         json.dump(image_index, f, indent=4)
    
#     print("JSON文件已生成: image_index.json")



import json

def create_oci_index_json(output_path, num=2, digests=None, sizes=None):
    # 设置默认的digests和sizes（如果未提供）
    default_digests = [
        "sha256:087d4ce3ce16c2c4b78806d70782443fe48d438c9ee70b75efc1cfbc282a5f23",
        "sha256:90f40df815ff2a5697f8d3c864520b375b984de9974b3a6427ec81d138ef86da"
    ]
    default_sizes = [7625, 2004]
    
    digests = digests or default_digests[:num]
    sizes = sizes or default_sizes[:num]
    
    # 验证输入
    if len(digests) < num or len(sizes) < num:
        raise ValueError(f"digests/sizes must have at least {num} elements")
    
    # 预定义的manifests模板
    manifest_templates = [
        {
            "mediaType": "application/vnd.oci.image.manifest.v1+json",
            "platform": {
                "architecture": "amd64",
                "os": "linux",
                "os.features": ["birentech.com/gpu", "106C"]
            },
            "annotations": {
                "moments8/torin": json.dumps({
                    "request": {"birentech.com/gpu": "1", "cpu": "32", "memory": "128Gi"},
                    "limit": {"birentech.com/gpu": "1", "cpu": "32", "memory": "128Gi"},
                    "tps": "32",
                    "nodeCapacity": "16",
                    "step": "2"
                })
            }
        },
        {
            "mediaType": "application/vnd.oci.image.manifest.v1+json",
            "platform": {
                "architecture": "amd64",
                "os": "linux",
                "os.features": ["streamcomputing.com/npu", "STCP920"]
            },
            "annotations": {
                "moments8/torin": json.dumps({
                    "request": {"streamcomputing.com/npu": "4", "cpu": "32", "memory": "128Gi"},
                    "limit": {"streamcomputing.com/npu": "4", "cpu": "32", "memory": "128Gi"},
                    "tps": "32",
                    "nodeCapacity": "16",
                    "step": "0"
                })
            }
        }
    ]
    
    # 根据num生成manifests列表
    manifests = []
    for i in range(num):
        manifest = manifest_templates[i].copy()
        manifest.update({
            "digest": digests[i],
            "size": sizes[i]
        })
        manifests.append(manifest)
    
    # 构建完整JSON结构
    oci_index = {
        "schemaVersion": 2,
        "mediaType": "application/vnd.oci.image.index.v1+json",
        "manifests": manifests,
        "annotations": {
            "moments8/torin": json.dumps({
                "request": {"moments8.com/gpu": "2", "cpu": "32", "memory": "128Gi"},
                "limit": {"moments8.com/gpu": "2", "cpu": "32", "memory": "512Gi"},
                "tps": 32,
                "modelDescription": "DeepSeek-R1-Distill-Qwen-7B是通过知识蒸馏技术从DeepSeek-R1模型中提炼出来的模型。它继承了DeepSeek-R1的推理能力，在数学、编程和推理等多个领域展现出卓越的性能。"
            })
        }
    }
    
    # 写入JSON文件
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(oci_index, f, indent=4, ensure_ascii=False)
    
    print(f"JSON文件已生成至: {output_path}")

# 使用示例
if __name__ == "__main__":
    # 示例1：生成包含2个manifest的文件（默认）
    create_oci_index_json("oci_index.json", num=2)
    
    # 示例2：生成只包含1个manifest的文件（自定义digest/size）
    # create_oci_index_json(
    #     output_path="oci_index_single.json",
    #     num=1,
    #     digests=["sha256:custom_digest_here"],
    #     sizes=[1234]
    # )