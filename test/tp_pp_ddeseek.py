import json
import os
import math
from transformers import AutoConfig

class VLLMResourceCalculator:
    def __init__(self, model_path):
        """
        初始化资源计算器
        :param model_path: Hugging Face模型路径（本地目录）
        """
        self.model_path = model_path
        self.config = self._load_config()
        self.params = self._calculate_params()
        self.valid_tp_values = self._get_valid_tp_values()

    def _load_config(self):
        """加载模型配置文件"""
        config_path = os.path.join(self.model_path, 'config.json')
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        with open(config_path, 'r') as f:
            config = json.load(f)
        return config

    def _calculate_params(self):
        """计算模型参数数量（基于标准Transformer架构）"""
        try:
            # 使用transformers库更准确地获取配置
            hf_config = AutoConfig.from_pretrained(self.model_path)
            hidden_size = hf_config.hidden_size
            num_layers = hf_config.num_hidden_layers
            num_heads = hf_config.num_attention_heads
            vocab_size = hf_config.vocab_size
            
            # 获取FFN中间层大小（不同模型名称可能不同）
            intermediate_size = getattr(hf_config, 'intermediate_size', None)
            if intermediate_size is None:
                intermediate_size = getattr(hf_config, 'n_inner', 4 * hidden_size)
        except Exception as e:
            # 回退到基本配置
            hidden_size = self.config.get('hidden_size', 4096)
            num_layers = self.config.get('num_hidden_layers', 32)
            num_heads = self.config.get('num_attention_heads', 32)
            vocab_size = self.config.get('vocab_size', 32000)
            intermediate_size = self.config.get('intermediate_size', 4 * hidden_size)

        # 参数计算公式
        embedding_params = vocab_size * hidden_size
        attention_params = 4 * hidden_size * hidden_size  # Q,K,V,O 投影
        ffn_params = 2 * hidden_size * intermediate_size  # 两个全连接层
        layer_params = (attention_params + ffn_params) * num_layers
        total_params = embedding_params + layer_params
        
        # 添加约5%的额外参数（层归一化、偏置等）
        return int(total_params * 1.05)

    def _get_valid_tp_values(self):
        """生成有效的TP值（2的幂且不超过注意力头数）"""
        num_heads = self.config.get('num_attention_heads', 1)
        valid_tp = []
        tp = 1
        while tp <= num_heads:
            valid_tp.append(tp)
            tp *= 2
        return valid_tp

    def calculate_gpu_requirements(self, gpu_mem_gb, seq_len=1024, batch_size=1, dtype_bytes=2, kv_cache_factor=1.5):
        """
        计算所需的最小GPU数量
        :param gpu_mem_gb: 单卡显存大小（GB）
        :param seq_len: 预期序列长度
        :param batch_size: 预期批处理大小
        :param dtype_bytes: 数据类型字节数（2=float16, 4=float32）
        :param kv_cache_factor: KV缓存额外因子
        :return: 最小GPU数量
        """
        # 基础模型内存（参数 + 梯度 + 优化器状态）
        model_mem = self.params * dtype_bytes * 3  # 保守估计
        
        # KV缓存内存（每个token）
        hidden_size = self.config.get('hidden_size', 4096)
        num_layers = self.config.get('num_hidden_layers', 32)
        kv_cache_per_token = 2 * num_layers * hidden_size * dtype_bytes
        
        # 总内存需求（字节）
        total_mem_bytes = model_mem + (batch_size * seq_len * kv_cache_per_token * kv_cache_factor)
        
        # 转换为GB并计算GPU数量
        total_mem_gb = total_mem_bytes / (1024 ** 3)
        return math.ceil(total_mem_gb / gpu_mem_gb)

    def recommend_parallelism(self, gpu_mem_gb):
        """
        推荐最优的TP/PP配置
        :param gpu_mem_gb: 单卡显存大小（GB）
        :return: (tp, pp, min_gpus) 元组
        """
        min_gpus = self.calculate_gpu_requirements(gpu_mem_gb)
        
        # 寻找最平衡的TP/PP组合
        best_tp, best_pp = 1, min_gpus
        min_diff = abs(1 - min_gpus)
        
        for tp in self.valid_tp_values:
            if tp > min_gpus:
                break
                
            pp = math.ceil(min_gpus / tp)
            total_gpus = tp * pp
            diff = abs(tp - pp)
            
            # 优先选择平衡且总GPU数最小的配置
            if diff < min_diff or (diff == min_diff and total_gpus < best_tp * best_pp):
                best_tp, best_pp = tp, pp
                min_diff = diff
        
        return best_tp, best_pp, min_gpus

# 使用示例
if __name__ == "__main__":
    # 初始化计算器（替换为实际模型路径）
    calculator = VLLMResourceCalculator("/path/to/huggingface/model")
    
    # 打印模型参数
    print(f"模型参数数量: {calculator.params:,}")
    
    # 计算8卡A100（80GB）需求
    gpu_mem = 80  # GB
    tp, pp, min_gpus = calculator.recommend_parallelism(gpu_mem)
    
    print(f"最小GPU需求: {min_gpus} 卡 ({gpu_mem}GB/卡)")
    print(f"推荐并行配置: TP={tp}, PP={pp}")
    print(f"所需实际GPU数量: {tp * pp}")