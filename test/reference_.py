import argparse
import os
import docker.errors
from modelscope.hub.snapshot_download import snapshot_download
import docker
import sys
from tempfile import TemporaryDirectory
from pathlib import Path
import shutil

def docker_login():
    pass

def download_from_modelscope(model_id, model_path):
    print("Step1: Download {} model to {}.".format(model_id, model_path))
    if os.path.exists(model_path):
        print("The model path exist, not need download!")
    else:
        os.makedirs(model_path, exist_ok=True)

    snapshot_download(
        model_id=model_id,
        local_dir=model_path,
        revision='master',  # master branch
    )
    print("{} model download complete!".format(model_id))

def ximu_convert_weights(model_id, model_path, ximu_convert_path, ximu_npu_num, ximu_convert_image):
    print("Step2: convert ximu weights to {}".format(ximu_convert_path))
    if os.path.exists(ximu_convert_path):
        print("The model path exist, not need download!")
    else:
        os.makedirs(ximu_convert_path, exist_ok=True)

    # create docker client
    client = docker.from_env()

    # check if the image exists
    try:
        iamge = client.images.get(ximu_convert_image)
        print("Image {} exist!".format(ximu_convert_image))
    except docker.errors.ImageNotFound:
        print("Local has no image!")
    
    # prepare to mount configuration
    volumes = {
        os.path.abspath(ximu_convert_path): {
            'bind': "/model_data/convert_data",
            'mode:': 'rw'
        },
        os.path.abspath(model_path): {
            'bind': "/model_data/model",
            'mode:': 'rw'
        },
    }

    # construct and execute commands
    command = (
        f"python3 -m stc_llm_dnn.tools.convert_weight "
        f"-m {model_id} "
        f"-p /model_data/model "
        f"-o /model_data/convert_data "
        f"-n {ximu_npu_num}"
    )
    
    # create container and excute python command
    container = client.containers.run(
        ximu_convert_image,
        command=command,
        volumes=volumes,
        detach=True,
        shm_size='64g',
        # remove=True
    )

    # get logs（return bytes）
    logs = container.logs()
    print(logs.decode())  # convert to string output

    result = container.wait()
    print(f"Container execution completed, exit status code: {result['StatusCode']}")

    if result['StatusCode'] == 0:
        print("Container exits normally, weight conversion successful!")
    else:
        print("Container exits abnormally, weight conversion failed!")
        sys.exit(1)

    container.remove()
    print("The converted weights have been saved to:", os.path.abspath(ximu_convert_path))

def build_image(base_image, new_image, model_weights, gpu_type):

    client = docker.from_env()

    # check if the image exists
    try:
        iamge = client.images.get(base_image)
        print("Image {} exist!".format(base_image))
    except docker.errors.ImageNotFound:
        print("Local has no image!")
        sys.exit(1)

    try:
        iamge = client.images.get(new_image)
        print("Image {} exist!".format(new_image))
        return
    except docker.errors.ImageNotFound:
        print(f"{new_image} not exit")

    # Dockerfile template
    dockerfile_template = """
    FROM {base_image}
    COPY ./weights /data/models
    """

    print(f"Build image: {new_image} ...")
    try:
        with TemporaryDirectory(prefix='docker_build_') as temp_dir:
            temp_dir_path = Path(temp_dir)

            # create docker file
            dockerfile_content = dockerfile_template.format(base_image=base_image,model_weights=model_weights)
            dockerfile_path = temp_dir_path / 'Dockerfile'
            with open(dockerfile_path, 'w') as f:
                f.write(dockerfile_content)
            
            # copy weights to tempt path
            weights_dest = temp_dir_path / 'weights'
            if weights_dest.exists():
                shutil.rmtree(weights_dest)
            shutil.copytree(str(model_weights), str(weights_dest))

            # create image
            image, logs = client.images.build(
                path=str(temp_dir_path),
                tag=new_image,
                rm=True,
                forcerm=True,
                platform="linux/amd64",
                buildargs={"GPU_TYPE":"{gpu_type}"},
            )

            print(f"Create image {new_image} success!")
            return image
        
    except Exception as e:
        print(f"Create failed: {new_image}, error: {e}")
        sys.exit(1)


def main():
    parser = argparse.ArgumentParser(description="import tools")
    parser.add_argument("--model_id", default="deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B", 
                        help="hugging face model")
    parser.add_argument("--model_path", default="./model", 
                        help="download model path")
    parser.add_argument("--ximu_convert_path", default="./ximu_weights", 
                        help="ximu convert weights path")
    parser.add_argument("--ximu_npu_num", type=int, default=2, 
                        help="ximu npu number")
    parser.add_argument("--ximu_convert_image", default="ximu_convert_weights:v1.0", 
                        help="ximu convert weights image")
    parser.add_argument("--ximu_origin_image", default="ximu_sdk:latest", 
                        help="ximu origin image")
    parser.add_argument("--ximu_npu_image", default="ximu_deepseek_1.5b:v1.0", 
                        help="ximu npu run model image")
    parser.add_argument("--biren_origin_image", default="*************/moments8/birensupa-vllm:25.05.10-py310-pt2.3.0-c024s001t001b15652-br1xx", 
                        help="biren origin image")
    parser.add_argument("--biren_gpu_image", default="biren_deepseek_1.5b:v1.0", 
                        help="biren gpu run model image")
    
    args = parser.parse_args()

    # step 1: download model from modelscope
    print("*"*100)
    download_from_modelscope(args.model_id, args.model_path)
    print("*"*100, "\n")

    #  step 2: ximu weights convert
    print("*"*100)
    ximu_convert_weights(args.model_id, args.model_path, args.ximu_convert_path, args.ximu_npu_num, args.ximu_convert_image)
    print("*"*100, "\n")

    # step 3: build biren and ximu images with model weights
    build_image(args.ximu_origin_image, args.ximu_npu_image, args.ximu_convert_path, "ximu")
    build_image(args.biren_origin_image, args.biren_gpu_image, args.model_path, "biren")


if __name__ == "__main__":
    main()