{"metadata": {"total_size": 65527752704}, "weight_map": {"model.embed_tokens.weight": "model-00001-of-000008.safetensors", "model.layers.0.self_attn.q_proj.bias": "model-00001-of-000008.safetensors", "model.layers.0.self_attn.k_proj.bias": "model-00001-of-000008.safetensors", "model.layers.0.self_attn.v_proj.bias": "model-00001-of-000008.safetensors", "model.layers.0.self_attn.q_proj.weight": "model-00001-of-000008.safetensors", "model.layers.0.self_attn.k_proj.weight": "model-00001-of-000008.safetensors", "model.layers.0.self_attn.v_proj.weight": "model-00001-of-000008.safetensors", "model.layers.0.self_attn.o_proj.weight": "model-00001-of-000008.safetensors", "model.layers.0.mlp.gate_proj.weight": "model-00001-of-000008.safetensors", "model.layers.0.mlp.up_proj.weight": "model-00001-of-000008.safetensors", "model.layers.0.mlp.down_proj.weight": "model-00001-of-000008.safetensors", "model.layers.0.input_layernorm.weight": "model-00001-of-000008.safetensors", "model.layers.0.post_attention_layernorm.weight": "model-00001-of-000008.safetensors", "model.layers.1.self_attn.q_proj.bias": "model-00001-of-000008.safetensors", "model.layers.1.self_attn.k_proj.bias": "model-00001-of-000008.safetensors", "model.layers.1.self_attn.v_proj.bias": "model-00001-of-000008.safetensors", "model.layers.1.self_attn.q_proj.weight": "model-00001-of-000008.safetensors", "model.layers.1.self_attn.k_proj.weight": "model-00001-of-000008.safetensors", "model.layers.1.self_attn.v_proj.weight": "model-00001-of-000008.safetensors", "model.layers.1.self_attn.o_proj.weight": "model-00001-of-000008.safetensors", "model.layers.1.mlp.gate_proj.weight": "model-00001-of-000008.safetensors", "model.layers.1.mlp.up_proj.weight": "model-00001-of-000008.safetensors", "model.layers.1.mlp.down_proj.weight": "model-00001-of-000008.safetensors", "model.layers.1.input_layernorm.weight": "model-00001-of-000008.safetensors", "model.layers.1.post_attention_layernorm.weight": "model-00001-of-000008.safetensors", "model.layers.2.self_attn.q_proj.bias": "model-00001-of-000008.safetensors", "model.layers.2.self_attn.k_proj.bias": "model-00001-of-000008.safetensors", "model.layers.2.self_attn.v_proj.bias": "model-00001-of-000008.safetensors", "model.layers.2.self_attn.q_proj.weight": "model-00001-of-000008.safetensors", "model.layers.2.self_attn.k_proj.weight": "model-00001-of-000008.safetensors", "model.layers.2.self_attn.v_proj.weight": "model-00001-of-000008.safetensors", "model.layers.2.self_attn.o_proj.weight": "model-00001-of-000008.safetensors", "model.layers.2.mlp.gate_proj.weight": "model-00001-of-000008.safetensors", "model.layers.2.mlp.up_proj.weight": "model-00001-of-000008.safetensors", "model.layers.2.mlp.down_proj.weight": "model-00001-of-000008.safetensors", "model.layers.2.input_layernorm.weight": "model-00001-of-000008.safetensors", "model.layers.2.post_attention_layernorm.weight": "model-00001-of-000008.safetensors", "model.layers.3.self_attn.q_proj.bias": "model-00001-of-000008.safetensors", "model.layers.3.self_attn.k_proj.bias": "model-00001-of-000008.safetensors", "model.layers.3.self_attn.v_proj.bias": "model-00001-of-000008.safetensors", "model.layers.3.self_attn.q_proj.weight": "model-00001-of-000008.safetensors", "model.layers.3.self_attn.k_proj.weight": "model-00001-of-000008.safetensors", "model.layers.3.self_attn.v_proj.weight": "model-00001-of-000008.safetensors", "model.layers.3.self_attn.o_proj.weight": "model-00001-of-000008.safetensors", "model.layers.3.mlp.gate_proj.weight": "model-00001-of-000008.safetensors", "model.layers.3.mlp.up_proj.weight": "model-00001-of-000008.safetensors", "model.layers.3.mlp.down_proj.weight": "model-00001-of-000008.safetensors", "model.layers.3.input_layernorm.weight": "model-00001-of-000008.safetensors", "model.layers.3.post_attention_layernorm.weight": "model-00001-of-000008.safetensors", "model.layers.4.self_attn.q_proj.bias": "model-00001-of-000008.safetensors", "model.layers.4.self_attn.k_proj.bias": "model-00001-of-000008.safetensors", "model.layers.4.self_attn.v_proj.bias": "model-00001-of-000008.safetensors", "model.layers.4.self_attn.q_proj.weight": "model-00001-of-000008.safetensors", "model.layers.4.self_attn.k_proj.weight": "model-00001-of-000008.safetensors", "model.layers.4.self_attn.v_proj.weight": "model-00001-of-000008.safetensors", "model.layers.4.self_attn.o_proj.weight": "model-00001-of-000008.safetensors", "model.layers.4.mlp.gate_proj.weight": "model-00001-of-000008.safetensors", "model.layers.4.mlp.up_proj.weight": "model-00001-of-000008.safetensors", "model.layers.4.mlp.down_proj.weight": "model-00001-of-000008.safetensors", "model.layers.4.input_layernorm.weight": "model-00001-of-000008.safetensors", "model.layers.4.post_attention_layernorm.weight": "model-00001-of-000008.safetensors", "model.layers.5.self_attn.q_proj.bias": "model-00001-of-000008.safetensors", "model.layers.5.self_attn.k_proj.bias": "model-00001-of-000008.safetensors", "model.layers.5.self_attn.v_proj.bias": "model-00001-of-000008.safetensors", "model.layers.5.self_attn.q_proj.weight": "model-00001-of-000008.safetensors", "model.layers.5.self_attn.k_proj.weight": "model-00001-of-000008.safetensors", "model.layers.5.self_attn.v_proj.weight": "model-00001-of-000008.safetensors", "model.layers.5.self_attn.o_proj.weight": "model-00001-of-000008.safetensors", "model.layers.5.mlp.gate_proj.weight": "model-00001-of-000008.safetensors", "model.layers.5.mlp.up_proj.weight": "model-00001-of-000008.safetensors", "model.layers.5.mlp.down_proj.weight": "model-00001-of-000008.safetensors", "model.layers.5.input_layernorm.weight": "model-00001-of-000008.safetensors", "model.layers.5.post_attention_layernorm.weight": "model-00001-of-000008.safetensors", "model.layers.6.self_attn.q_proj.bias": "model-00001-of-000008.safetensors", "model.layers.6.self_attn.k_proj.bias": "model-00001-of-000008.safetensors", "model.layers.6.self_attn.v_proj.bias": "model-00001-of-000008.safetensors", "model.layers.6.self_attn.q_proj.weight": "model-00001-of-000008.safetensors", "model.layers.6.self_attn.k_proj.weight": "model-00001-of-000008.safetensors", "model.layers.6.self_attn.v_proj.weight": "model-00001-of-000008.safetensors", "model.layers.6.self_attn.o_proj.weight": "model-00001-of-000008.safetensors", "model.layers.6.mlp.gate_proj.weight": "model-00001-of-000008.safetensors", "model.layers.6.mlp.up_proj.weight": "model-00001-of-000008.safetensors", "model.layers.6.mlp.down_proj.weight": "model-00001-of-000008.safetensors", "model.layers.6.input_layernorm.weight": "model-00001-of-000008.safetensors", "model.layers.6.post_attention_layernorm.weight": "model-00001-of-000008.safetensors", "model.layers.7.self_attn.q_proj.bias": "model-00001-of-000008.safetensors", "model.layers.7.self_attn.k_proj.bias": "model-00001-of-000008.safetensors", "model.layers.7.self_attn.v_proj.bias": "model-00001-of-000008.safetensors", "model.layers.7.self_attn.q_proj.weight": "model-00001-of-000008.safetensors", "model.layers.7.self_attn.k_proj.weight": "model-00001-of-000008.safetensors", "model.layers.7.self_attn.v_proj.weight": "model-00001-of-000008.safetensors", "model.layers.7.self_attn.o_proj.weight": "model-00001-of-000008.safetensors", "model.layers.7.mlp.gate_proj.weight": "model-00001-of-000008.safetensors", "model.layers.7.mlp.up_proj.weight": "model-00002-of-000008.safetensors", "model.layers.7.mlp.down_proj.weight": "model-00002-of-000008.safetensors", "model.layers.7.input_layernorm.weight": "model-00002-of-000008.safetensors", "model.layers.7.post_attention_layernorm.weight": "model-00002-of-000008.safetensors", "model.layers.8.self_attn.q_proj.bias": "model-00002-of-000008.safetensors", "model.layers.8.self_attn.k_proj.bias": "model-00002-of-000008.safetensors", "model.layers.8.self_attn.v_proj.bias": "model-00002-of-000008.safetensors", "model.layers.8.self_attn.q_proj.weight": "model-00002-of-000008.safetensors", "model.layers.8.self_attn.k_proj.weight": "model-00002-of-000008.safetensors", "model.layers.8.self_attn.v_proj.weight": "model-00002-of-000008.safetensors", "model.layers.8.self_attn.o_proj.weight": "model-00002-of-000008.safetensors", "model.layers.8.mlp.gate_proj.weight": "model-00002-of-000008.safetensors", "model.layers.8.mlp.up_proj.weight": "model-00002-of-000008.safetensors", "model.layers.8.mlp.down_proj.weight": "model-00002-of-000008.safetensors", "model.layers.8.input_layernorm.weight": "model-00002-of-000008.safetensors", "model.layers.8.post_attention_layernorm.weight": "model-00002-of-000008.safetensors", "model.layers.9.self_attn.q_proj.bias": "model-00002-of-000008.safetensors", "model.layers.9.self_attn.k_proj.bias": "model-00002-of-000008.safetensors", "model.layers.9.self_attn.v_proj.bias": "model-00002-of-000008.safetensors", "model.layers.9.self_attn.q_proj.weight": "model-00002-of-000008.safetensors", "model.layers.9.self_attn.k_proj.weight": "model-00002-of-000008.safetensors", "model.layers.9.self_attn.v_proj.weight": "model-00002-of-000008.safetensors", "model.layers.9.self_attn.o_proj.weight": "model-00002-of-000008.safetensors", "model.layers.9.mlp.gate_proj.weight": "model-00002-of-000008.safetensors", "model.layers.9.mlp.up_proj.weight": "model-00002-of-000008.safetensors", "model.layers.9.mlp.down_proj.weight": "model-00002-of-000008.safetensors", "model.layers.9.input_layernorm.weight": "model-00002-of-000008.safetensors", "model.layers.9.post_attention_layernorm.weight": "model-00002-of-000008.safetensors", "model.layers.10.self_attn.q_proj.bias": "model-00002-of-000008.safetensors", "model.layers.10.self_attn.k_proj.bias": "model-00002-of-000008.safetensors", "model.layers.10.self_attn.v_proj.bias": "model-00002-of-000008.safetensors", "model.layers.10.self_attn.q_proj.weight": "model-00002-of-000008.safetensors", "model.layers.10.self_attn.k_proj.weight": "model-00002-of-000008.safetensors", "model.layers.10.self_attn.v_proj.weight": "model-00002-of-000008.safetensors", "model.layers.10.self_attn.o_proj.weight": "model-00002-of-000008.safetensors", "model.layers.10.mlp.gate_proj.weight": "model-00002-of-000008.safetensors", "model.layers.10.mlp.up_proj.weight": "model-00002-of-000008.safetensors", "model.layers.10.mlp.down_proj.weight": "model-00002-of-000008.safetensors", "model.layers.10.input_layernorm.weight": "model-00002-of-000008.safetensors", "model.layers.10.post_attention_layernorm.weight": "model-00002-of-000008.safetensors", "model.layers.11.self_attn.q_proj.bias": "model-00002-of-000008.safetensors", "model.layers.11.self_attn.k_proj.bias": "model-00002-of-000008.safetensors", "model.layers.11.self_attn.v_proj.bias": "model-00002-of-000008.safetensors", "model.layers.11.self_attn.q_proj.weight": "model-00002-of-000008.safetensors", "model.layers.11.self_attn.k_proj.weight": "model-00002-of-000008.safetensors", "model.layers.11.self_attn.v_proj.weight": "model-00002-of-000008.safetensors", "model.layers.11.self_attn.o_proj.weight": "model-00002-of-000008.safetensors", "model.layers.11.mlp.gate_proj.weight": "model-00002-of-000008.safetensors", "model.layers.11.mlp.up_proj.weight": "model-00002-of-000008.safetensors", "model.layers.11.mlp.down_proj.weight": "model-00002-of-000008.safetensors", "model.layers.11.input_layernorm.weight": "model-00002-of-000008.safetensors", "model.layers.11.post_attention_layernorm.weight": "model-00002-of-000008.safetensors", "model.layers.12.self_attn.q_proj.bias": "model-00002-of-000008.safetensors", "model.layers.12.self_attn.k_proj.bias": "model-00002-of-000008.safetensors", "model.layers.12.self_attn.v_proj.bias": "model-00002-of-000008.safetensors", "model.layers.12.self_attn.q_proj.weight": "model-00002-of-000008.safetensors", "model.layers.12.self_attn.k_proj.weight": "model-00002-of-000008.safetensors", "model.layers.12.self_attn.v_proj.weight": "model-00002-of-000008.safetensors", "model.layers.12.self_attn.o_proj.weight": "model-00002-of-000008.safetensors", "model.layers.12.mlp.gate_proj.weight": "model-00002-of-000008.safetensors", "model.layers.12.mlp.up_proj.weight": "model-00002-of-000008.safetensors", "model.layers.12.mlp.down_proj.weight": "model-00002-of-000008.safetensors", "model.layers.12.input_layernorm.weight": "model-00002-of-000008.safetensors", "model.layers.12.post_attention_layernorm.weight": "model-00002-of-000008.safetensors", "model.layers.13.self_attn.q_proj.bias": "model-00002-of-000008.safetensors", "model.layers.13.self_attn.k_proj.bias": "model-00002-of-000008.safetensors", "model.layers.13.self_attn.v_proj.bias": "model-00002-of-000008.safetensors", "model.layers.13.self_attn.q_proj.weight": "model-00002-of-000008.safetensors", "model.layers.13.self_attn.k_proj.weight": "model-00002-of-000008.safetensors", "model.layers.13.self_attn.v_proj.weight": "model-00002-of-000008.safetensors", "model.layers.13.self_attn.o_proj.weight": "model-00002-of-000008.safetensors", "model.layers.13.mlp.gate_proj.weight": "model-00002-of-000008.safetensors", "model.layers.13.mlp.up_proj.weight": "model-00002-of-000008.safetensors", "model.layers.13.mlp.down_proj.weight": "model-00002-of-000008.safetensors", "model.layers.13.input_layernorm.weight": "model-00002-of-000008.safetensors", "model.layers.13.post_attention_layernorm.weight": "model-00002-of-000008.safetensors", "model.layers.14.self_attn.q_proj.bias": "model-00002-of-000008.safetensors", "model.layers.14.self_attn.k_proj.bias": "model-00002-of-000008.safetensors", "model.layers.14.self_attn.v_proj.bias": "model-00002-of-000008.safetensors", "model.layers.14.self_attn.q_proj.weight": "model-00002-of-000008.safetensors", "model.layers.14.self_attn.k_proj.weight": "model-00002-of-000008.safetensors", "model.layers.14.self_attn.v_proj.weight": "model-00002-of-000008.safetensors", "model.layers.14.self_attn.o_proj.weight": "model-00002-of-000008.safetensors", "model.layers.14.mlp.gate_proj.weight": "model-00002-of-000008.safetensors", "model.layers.14.mlp.up_proj.weight": "model-00002-of-000008.safetensors", "model.layers.14.mlp.down_proj.weight": "model-00002-of-000008.safetensors", "model.layers.14.input_layernorm.weight": "model-00002-of-000008.safetensors", "model.layers.14.post_attention_layernorm.weight": "model-00002-of-000008.safetensors", "model.layers.15.self_attn.q_proj.bias": "model-00002-of-000008.safetensors", "model.layers.15.self_attn.k_proj.bias": "model-00002-of-000008.safetensors", "model.layers.15.self_attn.v_proj.bias": "model-00002-of-000008.safetensors", "model.layers.15.self_attn.q_proj.weight": "model-00002-of-000008.safetensors", "model.layers.15.self_attn.k_proj.weight": "model-00002-of-000008.safetensors", "model.layers.15.self_attn.v_proj.weight": "model-00002-of-000008.safetensors", "model.layers.15.self_attn.o_proj.weight": "model-00002-of-000008.safetensors", "model.layers.15.mlp.gate_proj.weight": "model-00002-of-000008.safetensors", "model.layers.15.mlp.up_proj.weight": "model-00002-of-000008.safetensors", "model.layers.15.mlp.down_proj.weight": "model-00002-of-000008.safetensors", "model.layers.15.input_layernorm.weight": "model-00002-of-000008.safetensors", "model.layers.15.post_attention_layernorm.weight": "model-00002-of-000008.safetensors", "model.layers.16.self_attn.q_proj.bias": "model-00002-of-000008.safetensors", "model.layers.16.self_attn.k_proj.bias": "model-00002-of-000008.safetensors", "model.layers.16.self_attn.v_proj.bias": "model-00002-of-000008.safetensors", "model.layers.16.self_attn.q_proj.weight": "model-00002-of-000008.safetensors", "model.layers.16.self_attn.k_proj.weight": "model-00002-of-000008.safetensors", "model.layers.16.self_attn.v_proj.weight": "model-00002-of-000008.safetensors", "model.layers.16.self_attn.o_proj.weight": "model-00002-of-000008.safetensors", "model.layers.16.mlp.gate_proj.weight": "model-00002-of-000008.safetensors", "model.layers.16.mlp.up_proj.weight": "model-00003-of-000008.safetensors", "model.layers.16.mlp.down_proj.weight": "model-00003-of-000008.safetensors", "model.layers.16.input_layernorm.weight": "model-00003-of-000008.safetensors", "model.layers.16.post_attention_layernorm.weight": "model-00003-of-000008.safetensors", "model.layers.17.self_attn.q_proj.bias": "model-00003-of-000008.safetensors", "model.layers.17.self_attn.k_proj.bias": "model-00003-of-000008.safetensors", "model.layers.17.self_attn.v_proj.bias": "model-00003-of-000008.safetensors", "model.layers.17.self_attn.q_proj.weight": "model-00003-of-000008.safetensors", "model.layers.17.self_attn.k_proj.weight": "model-00003-of-000008.safetensors", "model.layers.17.self_attn.v_proj.weight": "model-00003-of-000008.safetensors", "model.layers.17.self_attn.o_proj.weight": "model-00003-of-000008.safetensors", "model.layers.17.mlp.gate_proj.weight": "model-00003-of-000008.safetensors", "model.layers.17.mlp.up_proj.weight": "model-00003-of-000008.safetensors", "model.layers.17.mlp.down_proj.weight": "model-00003-of-000008.safetensors", "model.layers.17.input_layernorm.weight": "model-00003-of-000008.safetensors", "model.layers.17.post_attention_layernorm.weight": "model-00003-of-000008.safetensors", "model.layers.18.self_attn.q_proj.bias": "model-00003-of-000008.safetensors", "model.layers.18.self_attn.k_proj.bias": "model-00003-of-000008.safetensors", "model.layers.18.self_attn.v_proj.bias": "model-00003-of-000008.safetensors", "model.layers.18.self_attn.q_proj.weight": "model-00003-of-000008.safetensors", "model.layers.18.self_attn.k_proj.weight": "model-00003-of-000008.safetensors", "model.layers.18.self_attn.v_proj.weight": "model-00003-of-000008.safetensors", "model.layers.18.self_attn.o_proj.weight": "model-00003-of-000008.safetensors", "model.layers.18.mlp.gate_proj.weight": "model-00003-of-000008.safetensors", "model.layers.18.mlp.up_proj.weight": "model-00003-of-000008.safetensors", "model.layers.18.mlp.down_proj.weight": "model-00003-of-000008.safetensors", "model.layers.18.input_layernorm.weight": "model-00003-of-000008.safetensors", "model.layers.18.post_attention_layernorm.weight": "model-00003-of-000008.safetensors", "model.layers.19.self_attn.q_proj.bias": "model-00003-of-000008.safetensors", "model.layers.19.self_attn.k_proj.bias": "model-00003-of-000008.safetensors", "model.layers.19.self_attn.v_proj.bias": "model-00003-of-000008.safetensors", "model.layers.19.self_attn.q_proj.weight": "model-00003-of-000008.safetensors", "model.layers.19.self_attn.k_proj.weight": "model-00003-of-000008.safetensors", "model.layers.19.self_attn.v_proj.weight": "model-00003-of-000008.safetensors", "model.layers.19.self_attn.o_proj.weight": "model-00003-of-000008.safetensors", "model.layers.19.mlp.gate_proj.weight": "model-00003-of-000008.safetensors", "model.layers.19.mlp.up_proj.weight": "model-00003-of-000008.safetensors", "model.layers.19.mlp.down_proj.weight": "model-00003-of-000008.safetensors", "model.layers.19.input_layernorm.weight": "model-00003-of-000008.safetensors", "model.layers.19.post_attention_layernorm.weight": "model-00003-of-000008.safetensors", "model.layers.20.self_attn.q_proj.bias": "model-00003-of-000008.safetensors", "model.layers.20.self_attn.k_proj.bias": "model-00003-of-000008.safetensors", "model.layers.20.self_attn.v_proj.bias": "model-00003-of-000008.safetensors", "model.layers.20.self_attn.q_proj.weight": "model-00003-of-000008.safetensors", "model.layers.20.self_attn.k_proj.weight": "model-00003-of-000008.safetensors", "model.layers.20.self_attn.v_proj.weight": "model-00003-of-000008.safetensors", "model.layers.20.self_attn.o_proj.weight": "model-00003-of-000008.safetensors", "model.layers.20.mlp.gate_proj.weight": "model-00003-of-000008.safetensors", "model.layers.20.mlp.up_proj.weight": "model-00003-of-000008.safetensors", "model.layers.20.mlp.down_proj.weight": "model-00003-of-000008.safetensors", "model.layers.20.input_layernorm.weight": "model-00003-of-000008.safetensors", "model.layers.20.post_attention_layernorm.weight": "model-00003-of-000008.safetensors", "model.layers.21.self_attn.q_proj.bias": "model-00003-of-000008.safetensors", "model.layers.21.self_attn.k_proj.bias": "model-00003-of-000008.safetensors", "model.layers.21.self_attn.v_proj.bias": "model-00003-of-000008.safetensors", "model.layers.21.self_attn.q_proj.weight": "model-00003-of-000008.safetensors", "model.layers.21.self_attn.k_proj.weight": "model-00003-of-000008.safetensors", "model.layers.21.self_attn.v_proj.weight": "model-00003-of-000008.safetensors", "model.layers.21.self_attn.o_proj.weight": "model-00003-of-000008.safetensors", "model.layers.21.mlp.gate_proj.weight": "model-00003-of-000008.safetensors", "model.layers.21.mlp.up_proj.weight": "model-00003-of-000008.safetensors", "model.layers.21.mlp.down_proj.weight": "model-00003-of-000008.safetensors", "model.layers.21.input_layernorm.weight": "model-00003-of-000008.safetensors", "model.layers.21.post_attention_layernorm.weight": "model-00003-of-000008.safetensors", "model.layers.22.self_attn.q_proj.bias": "model-00003-of-000008.safetensors", "model.layers.22.self_attn.k_proj.bias": "model-00003-of-000008.safetensors", "model.layers.22.self_attn.v_proj.bias": "model-00003-of-000008.safetensors", "model.layers.22.self_attn.q_proj.weight": "model-00003-of-000008.safetensors", "model.layers.22.self_attn.k_proj.weight": "model-00003-of-000008.safetensors", "model.layers.22.self_attn.v_proj.weight": "model-00003-of-000008.safetensors", "model.layers.22.self_attn.o_proj.weight": "model-00003-of-000008.safetensors", "model.layers.22.mlp.gate_proj.weight": "model-00003-of-000008.safetensors", "model.layers.22.mlp.up_proj.weight": "model-00003-of-000008.safetensors", "model.layers.22.mlp.down_proj.weight": "model-00003-of-000008.safetensors", "model.layers.22.input_layernorm.weight": "model-00003-of-000008.safetensors", "model.layers.22.post_attention_layernorm.weight": "model-00003-of-000008.safetensors", "model.layers.23.self_attn.q_proj.bias": "model-00003-of-000008.safetensors", "model.layers.23.self_attn.k_proj.bias": "model-00003-of-000008.safetensors", "model.layers.23.self_attn.v_proj.bias": "model-00003-of-000008.safetensors", "model.layers.23.self_attn.q_proj.weight": "model-00003-of-000008.safetensors", "model.layers.23.self_attn.k_proj.weight": "model-00003-of-000008.safetensors", "model.layers.23.self_attn.v_proj.weight": "model-00003-of-000008.safetensors", "model.layers.23.self_attn.o_proj.weight": "model-00003-of-000008.safetensors", "model.layers.23.mlp.gate_proj.weight": "model-00003-of-000008.safetensors", "model.layers.23.mlp.up_proj.weight": "model-00003-of-000008.safetensors", "model.layers.23.mlp.down_proj.weight": "model-00003-of-000008.safetensors", "model.layers.23.input_layernorm.weight": "model-00003-of-000008.safetensors", "model.layers.23.post_attention_layernorm.weight": "model-00003-of-000008.safetensors", "model.layers.24.self_attn.q_proj.bias": "model-00003-of-000008.safetensors", "model.layers.24.self_attn.k_proj.bias": "model-00003-of-000008.safetensors", "model.layers.24.self_attn.v_proj.bias": "model-00003-of-000008.safetensors", "model.layers.24.self_attn.q_proj.weight": "model-00003-of-000008.safetensors", "model.layers.24.self_attn.k_proj.weight": "model-00003-of-000008.safetensors", "model.layers.24.self_attn.v_proj.weight": "model-00003-of-000008.safetensors", "model.layers.24.self_attn.o_proj.weight": "model-00003-of-000008.safetensors", "model.layers.24.mlp.gate_proj.weight": "model-00003-of-000008.safetensors", "model.layers.24.mlp.up_proj.weight": "model-00003-of-000008.safetensors", "model.layers.24.mlp.down_proj.weight": "model-00003-of-000008.safetensors", "model.layers.24.input_layernorm.weight": "model-00003-of-000008.safetensors", "model.layers.24.post_attention_layernorm.weight": "model-00003-of-000008.safetensors", "model.layers.25.self_attn.q_proj.bias": "model-00003-of-000008.safetensors", "model.layers.25.self_attn.k_proj.bias": "model-00003-of-000008.safetensors", "model.layers.25.self_attn.v_proj.bias": "model-00003-of-000008.safetensors", "model.layers.25.self_attn.q_proj.weight": "model-00003-of-000008.safetensors", "model.layers.25.self_attn.k_proj.weight": "model-00003-of-000008.safetensors", "model.layers.25.self_attn.v_proj.weight": "model-00003-of-000008.safetensors", "model.layers.25.self_attn.o_proj.weight": "model-00003-of-000008.safetensors", "model.layers.25.mlp.gate_proj.weight": "model-00003-of-000008.safetensors", "model.layers.25.mlp.up_proj.weight": "model-00004-of-000008.safetensors", "model.layers.25.mlp.down_proj.weight": "model-00004-of-000008.safetensors", "model.layers.25.input_layernorm.weight": "model-00004-of-000008.safetensors", "model.layers.25.post_attention_layernorm.weight": "model-00004-of-000008.safetensors", "model.layers.26.self_attn.q_proj.bias": "model-00004-of-000008.safetensors", "model.layers.26.self_attn.k_proj.bias": "model-00004-of-000008.safetensors", "model.layers.26.self_attn.v_proj.bias": "model-00004-of-000008.safetensors", "model.layers.26.self_attn.q_proj.weight": "model-00004-of-000008.safetensors", "model.layers.26.self_attn.k_proj.weight": "model-00004-of-000008.safetensors", "model.layers.26.self_attn.v_proj.weight": "model-00004-of-000008.safetensors", "model.layers.26.self_attn.o_proj.weight": "model-00004-of-000008.safetensors", "model.layers.26.mlp.gate_proj.weight": "model-00004-of-000008.safetensors", "model.layers.26.mlp.up_proj.weight": "model-00004-of-000008.safetensors", "model.layers.26.mlp.down_proj.weight": "model-00004-of-000008.safetensors", "model.layers.26.input_layernorm.weight": "model-00004-of-000008.safetensors", "model.layers.26.post_attention_layernorm.weight": "model-00004-of-000008.safetensors", "model.layers.27.self_attn.q_proj.bias": "model-00004-of-000008.safetensors", "model.layers.27.self_attn.k_proj.bias": "model-00004-of-000008.safetensors", "model.layers.27.self_attn.v_proj.bias": "model-00004-of-000008.safetensors", "model.layers.27.self_attn.q_proj.weight": "model-00004-of-000008.safetensors", "model.layers.27.self_attn.k_proj.weight": "model-00004-of-000008.safetensors", "model.layers.27.self_attn.v_proj.weight": "model-00004-of-000008.safetensors", "model.layers.27.self_attn.o_proj.weight": "model-00004-of-000008.safetensors", "model.layers.27.mlp.gate_proj.weight": "model-00004-of-000008.safetensors", "model.layers.27.mlp.up_proj.weight": "model-00004-of-000008.safetensors", "model.layers.27.mlp.down_proj.weight": "model-00004-of-000008.safetensors", "model.layers.27.input_layernorm.weight": "model-00004-of-000008.safetensors", "model.layers.27.post_attention_layernorm.weight": "model-00004-of-000008.safetensors", "model.layers.28.self_attn.q_proj.bias": "model-00004-of-000008.safetensors", "model.layers.28.self_attn.k_proj.bias": "model-00004-of-000008.safetensors", "model.layers.28.self_attn.v_proj.bias": "model-00004-of-000008.safetensors", "model.layers.28.self_attn.q_proj.weight": "model-00004-of-000008.safetensors", "model.layers.28.self_attn.k_proj.weight": "model-00004-of-000008.safetensors", "model.layers.28.self_attn.v_proj.weight": "model-00004-of-000008.safetensors", "model.layers.28.self_attn.o_proj.weight": "model-00004-of-000008.safetensors", "model.layers.28.mlp.gate_proj.weight": "model-00004-of-000008.safetensors", "model.layers.28.mlp.up_proj.weight": "model-00004-of-000008.safetensors", "model.layers.28.mlp.down_proj.weight": "model-00004-of-000008.safetensors", "model.layers.28.input_layernorm.weight": "model-00004-of-000008.safetensors", "model.layers.28.post_attention_layernorm.weight": "model-00004-of-000008.safetensors", "model.layers.29.self_attn.q_proj.bias": "model-00004-of-000008.safetensors", "model.layers.29.self_attn.k_proj.bias": "model-00004-of-000008.safetensors", "model.layers.29.self_attn.v_proj.bias": "model-00004-of-000008.safetensors", "model.layers.29.self_attn.q_proj.weight": "model-00004-of-000008.safetensors", "model.layers.29.self_attn.k_proj.weight": "model-00004-of-000008.safetensors", "model.layers.29.self_attn.v_proj.weight": "model-00004-of-000008.safetensors", "model.layers.29.self_attn.o_proj.weight": "model-00004-of-000008.safetensors", "model.layers.29.mlp.gate_proj.weight": "model-00004-of-000008.safetensors", "model.layers.29.mlp.up_proj.weight": "model-00004-of-000008.safetensors", "model.layers.29.mlp.down_proj.weight": "model-00004-of-000008.safetensors", "model.layers.29.input_layernorm.weight": "model-00004-of-000008.safetensors", "model.layers.29.post_attention_layernorm.weight": "model-00004-of-000008.safetensors", "model.layers.30.self_attn.q_proj.bias": "model-00004-of-000008.safetensors", "model.layers.30.self_attn.k_proj.bias": "model-00004-of-000008.safetensors", "model.layers.30.self_attn.v_proj.bias": "model-00004-of-000008.safetensors", "model.layers.30.self_attn.q_proj.weight": "model-00004-of-000008.safetensors", "model.layers.30.self_attn.k_proj.weight": "model-00004-of-000008.safetensors", "model.layers.30.self_attn.v_proj.weight": "model-00004-of-000008.safetensors", "model.layers.30.self_attn.o_proj.weight": "model-00004-of-000008.safetensors", "model.layers.30.mlp.gate_proj.weight": "model-00004-of-000008.safetensors", "model.layers.30.mlp.up_proj.weight": "model-00004-of-000008.safetensors", "model.layers.30.mlp.down_proj.weight": "model-00004-of-000008.safetensors", "model.layers.30.input_layernorm.weight": "model-00004-of-000008.safetensors", "model.layers.30.post_attention_layernorm.weight": "model-00004-of-000008.safetensors", "model.layers.31.self_attn.q_proj.bias": "model-00004-of-000008.safetensors", "model.layers.31.self_attn.k_proj.bias": "model-00004-of-000008.safetensors", "model.layers.31.self_attn.v_proj.bias": "model-00004-of-000008.safetensors", "model.layers.31.self_attn.q_proj.weight": "model-00004-of-000008.safetensors", "model.layers.31.self_attn.k_proj.weight": "model-00004-of-000008.safetensors", "model.layers.31.self_attn.v_proj.weight": "model-00004-of-000008.safetensors", "model.layers.31.self_attn.o_proj.weight": "model-00004-of-000008.safetensors", "model.layers.31.mlp.gate_proj.weight": "model-00004-of-000008.safetensors", "model.layers.31.mlp.up_proj.weight": "model-00004-of-000008.safetensors", "model.layers.31.mlp.down_proj.weight": "model-00004-of-000008.safetensors", "model.layers.31.input_layernorm.weight": "model-00004-of-000008.safetensors", "model.layers.31.post_attention_layernorm.weight": "model-00004-of-000008.safetensors", "model.layers.32.self_attn.q_proj.bias": "model-00004-of-000008.safetensors", "model.layers.32.self_attn.k_proj.bias": "model-00004-of-000008.safetensors", "model.layers.32.self_attn.v_proj.bias": "model-00004-of-000008.safetensors", "model.layers.32.self_attn.q_proj.weight": "model-00004-of-000008.safetensors", "model.layers.32.self_attn.k_proj.weight": "model-00004-of-000008.safetensors", "model.layers.32.self_attn.v_proj.weight": "model-00004-of-000008.safetensors", "model.layers.32.self_attn.o_proj.weight": "model-00004-of-000008.safetensors", "model.layers.32.mlp.gate_proj.weight": "model-00004-of-000008.safetensors", "model.layers.32.mlp.up_proj.weight": "model-00004-of-000008.safetensors", "model.layers.32.mlp.down_proj.weight": "model-00004-of-000008.safetensors", "model.layers.32.input_layernorm.weight": "model-00004-of-000008.safetensors", "model.layers.32.post_attention_layernorm.weight": "model-00004-of-000008.safetensors", "model.layers.33.self_attn.q_proj.bias": "model-00004-of-000008.safetensors", "model.layers.33.self_attn.k_proj.bias": "model-00004-of-000008.safetensors", "model.layers.33.self_attn.v_proj.bias": "model-00004-of-000008.safetensors", "model.layers.33.self_attn.q_proj.weight": "model-00004-of-000008.safetensors", "model.layers.33.self_attn.k_proj.weight": "model-00004-of-000008.safetensors", "model.layers.33.self_attn.v_proj.weight": "model-00004-of-000008.safetensors", "model.layers.33.self_attn.o_proj.weight": "model-00004-of-000008.safetensors", "model.layers.33.mlp.gate_proj.weight": "model-00004-of-000008.safetensors", "model.layers.33.mlp.up_proj.weight": "model-00004-of-000008.safetensors", "model.layers.33.mlp.down_proj.weight": "model-00004-of-000008.safetensors", "model.layers.33.input_layernorm.weight": "model-00004-of-000008.safetensors", "model.layers.33.post_attention_layernorm.weight": "model-00004-of-000008.safetensors", "model.layers.34.self_attn.q_proj.bias": "model-00004-of-000008.safetensors", "model.layers.34.self_attn.k_proj.bias": "model-00004-of-000008.safetensors", "model.layers.34.self_attn.v_proj.bias": "model-00004-of-000008.safetensors", "model.layers.34.self_attn.q_proj.weight": "model-00004-of-000008.safetensors", "model.layers.34.self_attn.k_proj.weight": "model-00004-of-000008.safetensors", "model.layers.34.self_attn.v_proj.weight": "model-00004-of-000008.safetensors", "model.layers.34.self_attn.o_proj.weight": "model-00004-of-000008.safetensors", "model.layers.34.mlp.gate_proj.weight": "model-00004-of-000008.safetensors", "model.layers.34.mlp.up_proj.weight": "model-00005-of-000008.safetensors", "model.layers.34.mlp.down_proj.weight": "model-00005-of-000008.safetensors", "model.layers.34.input_layernorm.weight": "model-00005-of-000008.safetensors", "model.layers.34.post_attention_layernorm.weight": "model-00005-of-000008.safetensors", "model.layers.35.self_attn.q_proj.bias": "model-00005-of-000008.safetensors", "model.layers.35.self_attn.k_proj.bias": "model-00005-of-000008.safetensors", "model.layers.35.self_attn.v_proj.bias": "model-00005-of-000008.safetensors", "model.layers.35.self_attn.q_proj.weight": "model-00005-of-000008.safetensors", "model.layers.35.self_attn.k_proj.weight": "model-00005-of-000008.safetensors", "model.layers.35.self_attn.v_proj.weight": "model-00005-of-000008.safetensors", "model.layers.35.self_attn.o_proj.weight": "model-00005-of-000008.safetensors", "model.layers.35.mlp.gate_proj.weight": "model-00005-of-000008.safetensors", "model.layers.35.mlp.up_proj.weight": "model-00005-of-000008.safetensors", "model.layers.35.mlp.down_proj.weight": "model-00005-of-000008.safetensors", "model.layers.35.input_layernorm.weight": "model-00005-of-000008.safetensors", "model.layers.35.post_attention_layernorm.weight": "model-00005-of-000008.safetensors", "model.layers.36.self_attn.q_proj.bias": "model-00005-of-000008.safetensors", "model.layers.36.self_attn.k_proj.bias": "model-00005-of-000008.safetensors", "model.layers.36.self_attn.v_proj.bias": "model-00005-of-000008.safetensors", "model.layers.36.self_attn.q_proj.weight": "model-00005-of-000008.safetensors", "model.layers.36.self_attn.k_proj.weight": "model-00005-of-000008.safetensors", "model.layers.36.self_attn.v_proj.weight": "model-00005-of-000008.safetensors", "model.layers.36.self_attn.o_proj.weight": "model-00005-of-000008.safetensors", "model.layers.36.mlp.gate_proj.weight": "model-00005-of-000008.safetensors", "model.layers.36.mlp.up_proj.weight": "model-00005-of-000008.safetensors", "model.layers.36.mlp.down_proj.weight": "model-00005-of-000008.safetensors", "model.layers.36.input_layernorm.weight": "model-00005-of-000008.safetensors", "model.layers.36.post_attention_layernorm.weight": "model-00005-of-000008.safetensors", "model.layers.37.self_attn.q_proj.bias": "model-00005-of-000008.safetensors", "model.layers.37.self_attn.k_proj.bias": "model-00005-of-000008.safetensors", "model.layers.37.self_attn.v_proj.bias": "model-00005-of-000008.safetensors", "model.layers.37.self_attn.q_proj.weight": "model-00005-of-000008.safetensors", "model.layers.37.self_attn.k_proj.weight": "model-00005-of-000008.safetensors", "model.layers.37.self_attn.v_proj.weight": "model-00005-of-000008.safetensors", "model.layers.37.self_attn.o_proj.weight": "model-00005-of-000008.safetensors", "model.layers.37.mlp.gate_proj.weight": "model-00005-of-000008.safetensors", "model.layers.37.mlp.up_proj.weight": "model-00005-of-000008.safetensors", "model.layers.37.mlp.down_proj.weight": "model-00005-of-000008.safetensors", "model.layers.37.input_layernorm.weight": "model-00005-of-000008.safetensors", "model.layers.37.post_attention_layernorm.weight": "model-00005-of-000008.safetensors", "model.layers.38.self_attn.q_proj.bias": "model-00005-of-000008.safetensors", "model.layers.38.self_attn.k_proj.bias": "model-00005-of-000008.safetensors", "model.layers.38.self_attn.v_proj.bias": "model-00005-of-000008.safetensors", "model.layers.38.self_attn.q_proj.weight": "model-00005-of-000008.safetensors", "model.layers.38.self_attn.k_proj.weight": "model-00005-of-000008.safetensors", "model.layers.38.self_attn.v_proj.weight": "model-00005-of-000008.safetensors", "model.layers.38.self_attn.o_proj.weight": "model-00005-of-000008.safetensors", "model.layers.38.mlp.gate_proj.weight": "model-00005-of-000008.safetensors", "model.layers.38.mlp.up_proj.weight": "model-00005-of-000008.safetensors", "model.layers.38.mlp.down_proj.weight": "model-00005-of-000008.safetensors", "model.layers.38.input_layernorm.weight": "model-00005-of-000008.safetensors", "model.layers.38.post_attention_layernorm.weight": "model-00005-of-000008.safetensors", "model.layers.39.self_attn.q_proj.bias": "model-00005-of-000008.safetensors", "model.layers.39.self_attn.k_proj.bias": "model-00005-of-000008.safetensors", "model.layers.39.self_attn.v_proj.bias": "model-00005-of-000008.safetensors", "model.layers.39.self_attn.q_proj.weight": "model-00005-of-000008.safetensors", "model.layers.39.self_attn.k_proj.weight": "model-00005-of-000008.safetensors", "model.layers.39.self_attn.v_proj.weight": "model-00005-of-000008.safetensors", "model.layers.39.self_attn.o_proj.weight": "model-00005-of-000008.safetensors", "model.layers.39.mlp.gate_proj.weight": "model-00005-of-000008.safetensors", "model.layers.39.mlp.up_proj.weight": "model-00005-of-000008.safetensors", "model.layers.39.mlp.down_proj.weight": "model-00005-of-000008.safetensors", "model.layers.39.input_layernorm.weight": "model-00005-of-000008.safetensors", "model.layers.39.post_attention_layernorm.weight": "model-00005-of-000008.safetensors", "model.layers.40.self_attn.q_proj.bias": "model-00005-of-000008.safetensors", "model.layers.40.self_attn.k_proj.bias": "model-00005-of-000008.safetensors", "model.layers.40.self_attn.v_proj.bias": "model-00005-of-000008.safetensors", "model.layers.40.self_attn.q_proj.weight": "model-00005-of-000008.safetensors", "model.layers.40.self_attn.k_proj.weight": "model-00005-of-000008.safetensors", "model.layers.40.self_attn.v_proj.weight": "model-00005-of-000008.safetensors", "model.layers.40.self_attn.o_proj.weight": "model-00005-of-000008.safetensors", "model.layers.40.mlp.gate_proj.weight": "model-00005-of-000008.safetensors", "model.layers.40.mlp.up_proj.weight": "model-00005-of-000008.safetensors", "model.layers.40.mlp.down_proj.weight": "model-00005-of-000008.safetensors", "model.layers.40.input_layernorm.weight": "model-00005-of-000008.safetensors", "model.layers.40.post_attention_layernorm.weight": "model-00005-of-000008.safetensors", "model.layers.41.self_attn.q_proj.bias": "model-00005-of-000008.safetensors", "model.layers.41.self_attn.k_proj.bias": "model-00005-of-000008.safetensors", "model.layers.41.self_attn.v_proj.bias": "model-00005-of-000008.safetensors", "model.layers.41.self_attn.q_proj.weight": "model-00005-of-000008.safetensors", "model.layers.41.self_attn.k_proj.weight": "model-00005-of-000008.safetensors", "model.layers.41.self_attn.v_proj.weight": "model-00005-of-000008.safetensors", "model.layers.41.self_attn.o_proj.weight": "model-00005-of-000008.safetensors", "model.layers.41.mlp.gate_proj.weight": "model-00005-of-000008.safetensors", "model.layers.41.mlp.up_proj.weight": "model-00005-of-000008.safetensors", "model.layers.41.mlp.down_proj.weight": "model-00005-of-000008.safetensors", "model.layers.41.input_layernorm.weight": "model-00005-of-000008.safetensors", "model.layers.41.post_attention_layernorm.weight": "model-00005-of-000008.safetensors", "model.layers.42.self_attn.q_proj.bias": "model-00005-of-000008.safetensors", "model.layers.42.self_attn.k_proj.bias": "model-00005-of-000008.safetensors", "model.layers.42.self_attn.v_proj.bias": "model-00005-of-000008.safetensors", "model.layers.42.self_attn.q_proj.weight": "model-00005-of-000008.safetensors", "model.layers.42.self_attn.k_proj.weight": "model-00005-of-000008.safetensors", "model.layers.42.self_attn.v_proj.weight": "model-00005-of-000008.safetensors", "model.layers.42.self_attn.o_proj.weight": "model-00005-of-000008.safetensors", "model.layers.42.mlp.gate_proj.weight": "model-00005-of-000008.safetensors", "model.layers.42.mlp.up_proj.weight": "model-00005-of-000008.safetensors", "model.layers.42.mlp.down_proj.weight": "model-00005-of-000008.safetensors", "model.layers.42.input_layernorm.weight": "model-00005-of-000008.safetensors", "model.layers.42.post_attention_layernorm.weight": "model-00005-of-000008.safetensors", "model.layers.43.self_attn.q_proj.bias": "model-00005-of-000008.safetensors", "model.layers.43.self_attn.k_proj.bias": "model-00005-of-000008.safetensors", "model.layers.43.self_attn.v_proj.bias": "model-00005-of-000008.safetensors", "model.layers.43.self_attn.q_proj.weight": "model-00005-of-000008.safetensors", "model.layers.43.self_attn.k_proj.weight": "model-00005-of-000008.safetensors", "model.layers.43.self_attn.v_proj.weight": "model-00005-of-000008.safetensors", "model.layers.43.self_attn.o_proj.weight": "model-00005-of-000008.safetensors", "model.layers.43.mlp.gate_proj.weight": "model-00005-of-000008.safetensors", "model.layers.43.mlp.up_proj.weight": "model-00006-of-000008.safetensors", "model.layers.43.mlp.down_proj.weight": "model-00006-of-000008.safetensors", "model.layers.43.input_layernorm.weight": "model-00006-of-000008.safetensors", "model.layers.43.post_attention_layernorm.weight": "model-00006-of-000008.safetensors", "model.layers.44.self_attn.q_proj.bias": "model-00006-of-000008.safetensors", "model.layers.44.self_attn.k_proj.bias": "model-00006-of-000008.safetensors", "model.layers.44.self_attn.v_proj.bias": "model-00006-of-000008.safetensors", "model.layers.44.self_attn.q_proj.weight": "model-00006-of-000008.safetensors", "model.layers.44.self_attn.k_proj.weight": "model-00006-of-000008.safetensors", "model.layers.44.self_attn.v_proj.weight": "model-00006-of-000008.safetensors", "model.layers.44.self_attn.o_proj.weight": "model-00006-of-000008.safetensors", "model.layers.44.mlp.gate_proj.weight": "model-00006-of-000008.safetensors", "model.layers.44.mlp.up_proj.weight": "model-00006-of-000008.safetensors", "model.layers.44.mlp.down_proj.weight": "model-00006-of-000008.safetensors", "model.layers.44.input_layernorm.weight": "model-00006-of-000008.safetensors", "model.layers.44.post_attention_layernorm.weight": "model-00006-of-000008.safetensors", "model.layers.45.self_attn.q_proj.bias": "model-00006-of-000008.safetensors", "model.layers.45.self_attn.k_proj.bias": "model-00006-of-000008.safetensors", "model.layers.45.self_attn.v_proj.bias": "model-00006-of-000008.safetensors", "model.layers.45.self_attn.q_proj.weight": "model-00006-of-000008.safetensors", "model.layers.45.self_attn.k_proj.weight": "model-00006-of-000008.safetensors", "model.layers.45.self_attn.v_proj.weight": "model-00006-of-000008.safetensors", "model.layers.45.self_attn.o_proj.weight": "model-00006-of-000008.safetensors", "model.layers.45.mlp.gate_proj.weight": "model-00006-of-000008.safetensors", "model.layers.45.mlp.up_proj.weight": "model-00006-of-000008.safetensors", "model.layers.45.mlp.down_proj.weight": "model-00006-of-000008.safetensors", "model.layers.45.input_layernorm.weight": "model-00006-of-000008.safetensors", "model.layers.45.post_attention_layernorm.weight": "model-00006-of-000008.safetensors", "model.layers.46.self_attn.q_proj.bias": "model-00006-of-000008.safetensors", "model.layers.46.self_attn.k_proj.bias": "model-00006-of-000008.safetensors", "model.layers.46.self_attn.v_proj.bias": "model-00006-of-000008.safetensors", "model.layers.46.self_attn.q_proj.weight": "model-00006-of-000008.safetensors", "model.layers.46.self_attn.k_proj.weight": "model-00006-of-000008.safetensors", "model.layers.46.self_attn.v_proj.weight": "model-00006-of-000008.safetensors", "model.layers.46.self_attn.o_proj.weight": "model-00006-of-000008.safetensors", "model.layers.46.mlp.gate_proj.weight": "model-00006-of-000008.safetensors", "model.layers.46.mlp.up_proj.weight": "model-00006-of-000008.safetensors", "model.layers.46.mlp.down_proj.weight": "model-00006-of-000008.safetensors", "model.layers.46.input_layernorm.weight": "model-00006-of-000008.safetensors", "model.layers.46.post_attention_layernorm.weight": "model-00006-of-000008.safetensors", "model.layers.47.self_attn.q_proj.bias": "model-00006-of-000008.safetensors", "model.layers.47.self_attn.k_proj.bias": "model-00006-of-000008.safetensors", "model.layers.47.self_attn.v_proj.bias": "model-00006-of-000008.safetensors", "model.layers.47.self_attn.q_proj.weight": "model-00006-of-000008.safetensors", "model.layers.47.self_attn.k_proj.weight": "model-00006-of-000008.safetensors", "model.layers.47.self_attn.v_proj.weight": "model-00006-of-000008.safetensors", "model.layers.47.self_attn.o_proj.weight": "model-00006-of-000008.safetensors", "model.layers.47.mlp.gate_proj.weight": "model-00006-of-000008.safetensors", "model.layers.47.mlp.up_proj.weight": "model-00006-of-000008.safetensors", "model.layers.47.mlp.down_proj.weight": "model-00006-of-000008.safetensors", "model.layers.47.input_layernorm.weight": "model-00006-of-000008.safetensors", "model.layers.47.post_attention_layernorm.weight": "model-00006-of-000008.safetensors", "model.layers.48.self_attn.q_proj.bias": "model-00006-of-000008.safetensors", "model.layers.48.self_attn.k_proj.bias": "model-00006-of-000008.safetensors", "model.layers.48.self_attn.v_proj.bias": "model-00006-of-000008.safetensors", "model.layers.48.self_attn.q_proj.weight": "model-00006-of-000008.safetensors", "model.layers.48.self_attn.k_proj.weight": "model-00006-of-000008.safetensors", "model.layers.48.self_attn.v_proj.weight": "model-00006-of-000008.safetensors", "model.layers.48.self_attn.o_proj.weight": "model-00006-of-000008.safetensors", "model.layers.48.mlp.gate_proj.weight": "model-00006-of-000008.safetensors", "model.layers.48.mlp.up_proj.weight": "model-00006-of-000008.safetensors", "model.layers.48.mlp.down_proj.weight": "model-00006-of-000008.safetensors", "model.layers.48.input_layernorm.weight": "model-00006-of-000008.safetensors", "model.layers.48.post_attention_layernorm.weight": "model-00006-of-000008.safetensors", "model.layers.49.self_attn.q_proj.bias": "model-00006-of-000008.safetensors", "model.layers.49.self_attn.k_proj.bias": "model-00006-of-000008.safetensors", "model.layers.49.self_attn.v_proj.bias": "model-00006-of-000008.safetensors", "model.layers.49.self_attn.q_proj.weight": "model-00006-of-000008.safetensors", "model.layers.49.self_attn.k_proj.weight": "model-00006-of-000008.safetensors", "model.layers.49.self_attn.v_proj.weight": "model-00006-of-000008.safetensors", "model.layers.49.self_attn.o_proj.weight": "model-00006-of-000008.safetensors", "model.layers.49.mlp.gate_proj.weight": "model-00006-of-000008.safetensors", "model.layers.49.mlp.up_proj.weight": "model-00006-of-000008.safetensors", "model.layers.49.mlp.down_proj.weight": "model-00006-of-000008.safetensors", "model.layers.49.input_layernorm.weight": "model-00006-of-000008.safetensors", "model.layers.49.post_attention_layernorm.weight": "model-00006-of-000008.safetensors", "model.layers.50.self_attn.q_proj.bias": "model-00006-of-000008.safetensors", "model.layers.50.self_attn.k_proj.bias": "model-00006-of-000008.safetensors", "model.layers.50.self_attn.v_proj.bias": "model-00006-of-000008.safetensors", "model.layers.50.self_attn.q_proj.weight": "model-00006-of-000008.safetensors", "model.layers.50.self_attn.k_proj.weight": "model-00006-of-000008.safetensors", "model.layers.50.self_attn.v_proj.weight": "model-00006-of-000008.safetensors", "model.layers.50.self_attn.o_proj.weight": "model-00006-of-000008.safetensors", "model.layers.50.mlp.gate_proj.weight": "model-00006-of-000008.safetensors", "model.layers.50.mlp.up_proj.weight": "model-00006-of-000008.safetensors", "model.layers.50.mlp.down_proj.weight": "model-00006-of-000008.safetensors", "model.layers.50.input_layernorm.weight": "model-00006-of-000008.safetensors", "model.layers.50.post_attention_layernorm.weight": "model-00006-of-000008.safetensors", "model.layers.51.self_attn.q_proj.bias": "model-00006-of-000008.safetensors", "model.layers.51.self_attn.k_proj.bias": "model-00006-of-000008.safetensors", "model.layers.51.self_attn.v_proj.bias": "model-00006-of-000008.safetensors", "model.layers.51.self_attn.q_proj.weight": "model-00006-of-000008.safetensors", "model.layers.51.self_attn.k_proj.weight": "model-00006-of-000008.safetensors", "model.layers.51.self_attn.v_proj.weight": "model-00006-of-000008.safetensors", "model.layers.51.self_attn.o_proj.weight": "model-00006-of-000008.safetensors", "model.layers.51.mlp.gate_proj.weight": "model-00006-of-000008.safetensors", "model.layers.51.mlp.up_proj.weight": "model-00006-of-000008.safetensors", "model.layers.51.mlp.down_proj.weight": "model-00006-of-000008.safetensors", "model.layers.51.input_layernorm.weight": "model-00006-of-000008.safetensors", "model.layers.51.post_attention_layernorm.weight": "model-00006-of-000008.safetensors", "model.layers.52.self_attn.q_proj.bias": "model-00006-of-000008.safetensors", "model.layers.52.self_attn.k_proj.bias": "model-00006-of-000008.safetensors", "model.layers.52.self_attn.v_proj.bias": "model-00006-of-000008.safetensors", "model.layers.52.self_attn.q_proj.weight": "model-00006-of-000008.safetensors", "model.layers.52.self_attn.k_proj.weight": "model-00006-of-000008.safetensors", "model.layers.52.self_attn.v_proj.weight": "model-00006-of-000008.safetensors", "model.layers.52.self_attn.o_proj.weight": "model-00006-of-000008.safetensors", "model.layers.52.mlp.gate_proj.weight": "model-00006-of-000008.safetensors", "model.layers.52.mlp.up_proj.weight": "model-00007-of-000008.safetensors", "model.layers.52.mlp.down_proj.weight": "model-00007-of-000008.safetensors", "model.layers.52.input_layernorm.weight": "model-00007-of-000008.safetensors", "model.layers.52.post_attention_layernorm.weight": "model-00007-of-000008.safetensors", "model.layers.53.self_attn.q_proj.bias": "model-00007-of-000008.safetensors", "model.layers.53.self_attn.k_proj.bias": "model-00007-of-000008.safetensors", "model.layers.53.self_attn.v_proj.bias": "model-00007-of-000008.safetensors", "model.layers.53.self_attn.q_proj.weight": "model-00007-of-000008.safetensors", "model.layers.53.self_attn.k_proj.weight": "model-00007-of-000008.safetensors", "model.layers.53.self_attn.v_proj.weight": "model-00007-of-000008.safetensors", "model.layers.53.self_attn.o_proj.weight": "model-00007-of-000008.safetensors", "model.layers.53.mlp.gate_proj.weight": "model-00007-of-000008.safetensors", "model.layers.53.mlp.up_proj.weight": "model-00007-of-000008.safetensors", "model.layers.53.mlp.down_proj.weight": "model-00007-of-000008.safetensors", "model.layers.53.input_layernorm.weight": "model-00007-of-000008.safetensors", "model.layers.53.post_attention_layernorm.weight": "model-00007-of-000008.safetensors", "model.layers.54.self_attn.q_proj.bias": "model-00007-of-000008.safetensors", "model.layers.54.self_attn.k_proj.bias": "model-00007-of-000008.safetensors", "model.layers.54.self_attn.v_proj.bias": "model-00007-of-000008.safetensors", "model.layers.54.self_attn.q_proj.weight": "model-00007-of-000008.safetensors", "model.layers.54.self_attn.k_proj.weight": "model-00007-of-000008.safetensors", "model.layers.54.self_attn.v_proj.weight": "model-00007-of-000008.safetensors", "model.layers.54.self_attn.o_proj.weight": "model-00007-of-000008.safetensors", "model.layers.54.mlp.gate_proj.weight": "model-00007-of-000008.safetensors", "model.layers.54.mlp.up_proj.weight": "model-00007-of-000008.safetensors", "model.layers.54.mlp.down_proj.weight": "model-00007-of-000008.safetensors", "model.layers.54.input_layernorm.weight": "model-00007-of-000008.safetensors", "model.layers.54.post_attention_layernorm.weight": "model-00007-of-000008.safetensors", "model.layers.55.self_attn.q_proj.bias": "model-00007-of-000008.safetensors", "model.layers.55.self_attn.k_proj.bias": "model-00007-of-000008.safetensors", "model.layers.55.self_attn.v_proj.bias": "model-00007-of-000008.safetensors", "model.layers.55.self_attn.q_proj.weight": "model-00007-of-000008.safetensors", "model.layers.55.self_attn.k_proj.weight": "model-00007-of-000008.safetensors", "model.layers.55.self_attn.v_proj.weight": "model-00007-of-000008.safetensors", "model.layers.55.self_attn.o_proj.weight": "model-00007-of-000008.safetensors", "model.layers.55.mlp.gate_proj.weight": "model-00007-of-000008.safetensors", "model.layers.55.mlp.up_proj.weight": "model-00007-of-000008.safetensors", "model.layers.55.mlp.down_proj.weight": "model-00007-of-000008.safetensors", "model.layers.55.input_layernorm.weight": "model-00007-of-000008.safetensors", "model.layers.55.post_attention_layernorm.weight": "model-00007-of-000008.safetensors", "model.layers.56.self_attn.q_proj.bias": "model-00007-of-000008.safetensors", "model.layers.56.self_attn.k_proj.bias": "model-00007-of-000008.safetensors", "model.layers.56.self_attn.v_proj.bias": "model-00007-of-000008.safetensors", "model.layers.56.self_attn.q_proj.weight": "model-00007-of-000008.safetensors", "model.layers.56.self_attn.k_proj.weight": "model-00007-of-000008.safetensors", "model.layers.56.self_attn.v_proj.weight": "model-00007-of-000008.safetensors", "model.layers.56.self_attn.o_proj.weight": "model-00007-of-000008.safetensors", "model.layers.56.mlp.gate_proj.weight": "model-00007-of-000008.safetensors", "model.layers.56.mlp.up_proj.weight": "model-00007-of-000008.safetensors", "model.layers.56.mlp.down_proj.weight": "model-00007-of-000008.safetensors", "model.layers.56.input_layernorm.weight": "model-00007-of-000008.safetensors", "model.layers.56.post_attention_layernorm.weight": "model-00007-of-000008.safetensors", "model.layers.57.self_attn.q_proj.bias": "model-00007-of-000008.safetensors", "model.layers.57.self_attn.k_proj.bias": "model-00007-of-000008.safetensors", "model.layers.57.self_attn.v_proj.bias": "model-00007-of-000008.safetensors", "model.layers.57.self_attn.q_proj.weight": "model-00007-of-000008.safetensors", "model.layers.57.self_attn.k_proj.weight": "model-00007-of-000008.safetensors", "model.layers.57.self_attn.v_proj.weight": "model-00007-of-000008.safetensors", "model.layers.57.self_attn.o_proj.weight": "model-00007-of-000008.safetensors", "model.layers.57.mlp.gate_proj.weight": "model-00007-of-000008.safetensors", "model.layers.57.mlp.up_proj.weight": "model-00007-of-000008.safetensors", "model.layers.57.mlp.down_proj.weight": "model-00007-of-000008.safetensors", "model.layers.57.input_layernorm.weight": "model-00007-of-000008.safetensors", "model.layers.57.post_attention_layernorm.weight": "model-00007-of-000008.safetensors", "model.layers.58.self_attn.q_proj.bias": "model-00007-of-000008.safetensors", "model.layers.58.self_attn.k_proj.bias": "model-00007-of-000008.safetensors", "model.layers.58.self_attn.v_proj.bias": "model-00007-of-000008.safetensors", "model.layers.58.self_attn.q_proj.weight": "model-00007-of-000008.safetensors", "model.layers.58.self_attn.k_proj.weight": "model-00007-of-000008.safetensors", "model.layers.58.self_attn.v_proj.weight": "model-00007-of-000008.safetensors", "model.layers.58.self_attn.o_proj.weight": "model-00007-of-000008.safetensors", "model.layers.58.mlp.gate_proj.weight": "model-00007-of-000008.safetensors", "model.layers.58.mlp.up_proj.weight": "model-00007-of-000008.safetensors", "model.layers.58.mlp.down_proj.weight": "model-00007-of-000008.safetensors", "model.layers.58.input_layernorm.weight": "model-00007-of-000008.safetensors", "model.layers.58.post_attention_layernorm.weight": "model-00007-of-000008.safetensors", "model.layers.59.self_attn.q_proj.bias": "model-00007-of-000008.safetensors", "model.layers.59.self_attn.k_proj.bias": "model-00007-of-000008.safetensors", "model.layers.59.self_attn.v_proj.bias": "model-00007-of-000008.safetensors", "model.layers.59.self_attn.q_proj.weight": "model-00007-of-000008.safetensors", "model.layers.59.self_attn.k_proj.weight": "model-00007-of-000008.safetensors", "model.layers.59.self_attn.v_proj.weight": "model-00007-of-000008.safetensors", "model.layers.59.self_attn.o_proj.weight": "model-00007-of-000008.safetensors", "model.layers.59.mlp.gate_proj.weight": "model-00007-of-000008.safetensors", "model.layers.59.mlp.up_proj.weight": "model-00007-of-000008.safetensors", "model.layers.59.mlp.down_proj.weight": "model-00007-of-000008.safetensors", "model.layers.59.input_layernorm.weight": "model-00007-of-000008.safetensors", "model.layers.59.post_attention_layernorm.weight": "model-00007-of-000008.safetensors", "model.layers.60.self_attn.q_proj.bias": "model-00007-of-000008.safetensors", "model.layers.60.self_attn.k_proj.bias": "model-00007-of-000008.safetensors", "model.layers.60.self_attn.v_proj.bias": "model-00007-of-000008.safetensors", "model.layers.60.self_attn.q_proj.weight": "model-00007-of-000008.safetensors", "model.layers.60.self_attn.k_proj.weight": "model-00007-of-000008.safetensors", "model.layers.60.self_attn.v_proj.weight": "model-00007-of-000008.safetensors", "model.layers.60.self_attn.o_proj.weight": "model-00007-of-000008.safetensors", "model.layers.60.mlp.gate_proj.weight": "model-00007-of-000008.safetensors", "model.layers.60.mlp.up_proj.weight": "model-00007-of-000008.safetensors", "model.layers.60.mlp.down_proj.weight": "model-00007-of-000008.safetensors", "model.layers.60.input_layernorm.weight": "model-00007-of-000008.safetensors", "model.layers.60.post_attention_layernorm.weight": "model-00007-of-000008.safetensors", "model.layers.61.self_attn.q_proj.bias": "model-00007-of-000008.safetensors", "model.layers.61.self_attn.k_proj.bias": "model-00007-of-000008.safetensors", "model.layers.61.self_attn.v_proj.bias": "model-00007-of-000008.safetensors", "model.layers.61.self_attn.q_proj.weight": "model-00007-of-000008.safetensors", "model.layers.61.self_attn.k_proj.weight": "model-00007-of-000008.safetensors", "model.layers.61.self_attn.v_proj.weight": "model-00007-of-000008.safetensors", "model.layers.61.self_attn.o_proj.weight": "model-00007-of-000008.safetensors", "model.layers.61.mlp.gate_proj.weight": "model-00007-of-000008.safetensors", "model.layers.61.mlp.up_proj.weight": "model-00008-of-000008.safetensors", "model.layers.61.mlp.down_proj.weight": "model-00008-of-000008.safetensors", "model.layers.61.input_layernorm.weight": "model-00008-of-000008.safetensors", "model.layers.61.post_attention_layernorm.weight": "model-00008-of-000008.safetensors", "model.layers.62.self_attn.q_proj.bias": "model-00008-of-000008.safetensors", "model.layers.62.self_attn.k_proj.bias": "model-00008-of-000008.safetensors", "model.layers.62.self_attn.v_proj.bias": "model-00008-of-000008.safetensors", "model.layers.62.self_attn.q_proj.weight": "model-00008-of-000008.safetensors", "model.layers.62.self_attn.k_proj.weight": "model-00008-of-000008.safetensors", "model.layers.62.self_attn.v_proj.weight": "model-00008-of-000008.safetensors", "model.layers.62.self_attn.o_proj.weight": "model-00008-of-000008.safetensors", "model.layers.62.mlp.gate_proj.weight": "model-00008-of-000008.safetensors", "model.layers.62.mlp.up_proj.weight": "model-00008-of-000008.safetensors", "model.layers.62.mlp.down_proj.weight": "model-00008-of-000008.safetensors", "model.layers.62.input_layernorm.weight": "model-00008-of-000008.safetensors", "model.layers.62.post_attention_layernorm.weight": "model-00008-of-000008.safetensors", "model.layers.63.self_attn.q_proj.bias": "model-00008-of-000008.safetensors", "model.layers.63.self_attn.k_proj.bias": "model-00008-of-000008.safetensors", "model.layers.63.self_attn.v_proj.bias": "model-00008-of-000008.safetensors", "model.layers.63.self_attn.q_proj.weight": "model-00008-of-000008.safetensors", "model.layers.63.self_attn.k_proj.weight": "model-00008-of-000008.safetensors", "model.layers.63.self_attn.v_proj.weight": "model-00008-of-000008.safetensors", "model.layers.63.self_attn.o_proj.weight": "model-00008-of-000008.safetensors", "model.layers.63.mlp.gate_proj.weight": "model-00008-of-000008.safetensors", "model.layers.63.mlp.up_proj.weight": "model-00008-of-000008.safetensors", "model.layers.63.mlp.down_proj.weight": "model-00008-of-000008.safetensors", "model.layers.63.input_layernorm.weight": "model-00008-of-000008.safetensors", "model.layers.63.post_attention_layernorm.weight": "model-00008-of-000008.safetensors", "model.norm.weight": "model-00008-of-000008.safetensors", "lm_head.weight": "model-00008-of-000008.safetensors"}}