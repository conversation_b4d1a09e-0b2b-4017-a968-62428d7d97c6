import os
import json
import math
from typing import Tuple, Optional, Dict, Any

class VLLMModelAnalyzer:
    def __init__(self, model_path: str):
        """
        初始化模型分析器
        
        参数:
            model_path (str): HuggingFace模型的本地路径
        """
        self.model_path = model_path
        self.config = self._load_config()
        self.parameter_bytes = self._calculate_parameter_bytes()

    def _load_config(self) -> Dict[str, Any]:
        """加载模型的config.json文件"""
        config_path = os.path.join(self.model_path, 'config.json')
        with open(config_path, 'r', encoding='utf-8') as f:
            return json.load(f)

    def _calculate_parameter_bytes(self) -> int:
        """
        计算模型参数的字节大小
        
        策略:
        1. 优先使用配置文件中的`num_parameters`
        2. 若未提供，通过结构公式估算
        3. 假设使用float16类型（每个参数2字节）
        """
        # 尝试从配置中直接获取参数数量
        num_params = self.config.get('num_parameters', None)
        
        # 若未提供，通过结构估算
        if num_params is None:
            num_hidden_layers = self.config['num_hidden_layers']
            hidden_size = self.config['hidden_size']
            intermediate_size = self.config.get('intermediate_size', 4 * hidden_size)
            num_attention_heads = self.config['num_attention_heads']
            
            # 通用transformer参数估算公式
            # 嵌入层 + 各层参数 + LM头部
            embed_params = self.config['vocab_size'] * hidden_size
            per_layer = (
                4 * hidden_size * hidden_size +  # Q/K/V/O投影
                2 * hidden_size * intermediate_size  # FFN
            )
            num_params = embed_params + num_hidden_layers * per_layer

        # 返回float16格式下的字节大小
        return num_params * 2  # 每个参数2字节

    def get_parameter_size(self) -> Tuple[int, str]:
        """
        获取模型参数大小（可读格式）
        
        返回:
            tuple: 
                - int: 参数字节数
                - str: 人类可读的大小字符串
        """
        size_bytes = self.parameter_bytes
        
        # 转换为可读单位
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size_bytes < 1024:
                return size_bytes, f"{size_bytes:.2f} {unit}"
            size_bytes /= 1024
        return size_bytes, f"{size_bytes:.2f} PB"

    def min_gpus_required(self, vram_per_gpu: int, kv_cache_factor: float = 1.5) -> int:
        """
        计算推理所需的最少显卡数量
        
        参数:
            vram_per_gpu (int): 单张显卡的显存大小（GB）
            kv_cache_factor (float): KV缓存因子（经验值，默认1.5）
        
        返回:
            int: 需要的最少显卡数量
        """
        # 转换为GB
        params_gb = self.parameter_bytes / (1024 ** 3)
        
        # 总显存需求 = 模型参数 + KV缓存
        total_vram_required = params_gb * (1 + kv_cache_factor)
        
        # 计算最少显卡数量
        min_gpus = math.ceil(total_vram_required / vram_per_gpu)
        return max(1, min_gpus)  # 至少需要1张显卡

    def recommend_parallel_config(self, num_gpus: int) -> Tuple[int, int]:
        """
        推荐最优的TP和PP配置值（尽可能相等）
        
        参数:
            num_gpus (int): 可用的显卡数量
        
        返回:
            tuple: (tp_size, pp_size)
        """
        # 获取模型结构限制
        num_layers = self.config['num_hidden_layers']
        num_heads = self.config['num_attention_heads']
        
        # 存储所有有效分解组合
        valid_decompositions = []
        
        # 遍历所有可能的分解组合
        for tp_size in range(1, num_gpus + 1):
            if num_gpus % tp_size != 0:
                continue
                
            pp_size = num_gpus // tp_size
            
            # 检查分解是否满足模型约束
            if pp_size > 1 and num_layers % pp_size != 0:
                continue  # PP需要能整除网络层数
            if num_heads % tp_size != 0:
                continue  # TP需要能整除注意力头数
                
            # 计算TP/PP的平衡得分（越接近1越好）
            balance_score = abs(math.log2(tp_size / pp_size))
            valid_decompositions.append((tp_size, pp_size, balance_score))
        
        # 如果没有有效分解，返回默认值
        if not valid_decompositions:
            return (1, 1)  # 默认使用数据并行
        
        # 选择最平衡的分解
        valid_decompositions.sort(key=lambda x: x[2])  # 按平衡得分排序
        best_tp, best_pp, _ = valid_decompositions[0]
        
        return best_tp, best_pp

# 示例用法
if __name__ == "__main__":
    # 初始化分析器（替换为实际模型路径）
    analyzer = VLLMModelAnalyzer("/path/to/your/model")
    
    # 1. 查看模型参数大小
    param_bytes, readable_size = analyzer.get_parameter_size()
    print(f"模型参数大小: {readable_size}")
    
    # 2. 计算需要多少张显卡（示例GPU显存为80GB）
    gpu_mem_gb = 80
    min_gpus = analyzer.min_gpus_required(gpu_mem_gb)
    print(f"最少需要GPU数量: {min_gpus} (每张卡{gpu_mem_gb}GB显存)")
    
    # 3. 推荐最优TP/PP配置
    tp, pp = analyzer.recommend_parallel_config(min_gpus)
    print(f"推荐并行配置: TP={tp}, PP={pp} (总计GPU: {tp * pp})")