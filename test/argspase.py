import argparse

def main():
    # 1. 创建顶级解析器
    parser = argparse.ArgumentParser(prog='app')
    subparsers = parser.add_subparsers(dest='command', help='主命令')

    # 2. 一级子命令: db
    db_parser = subparsers.add_parser('db', help='数据库操作')
    db_subparsers = db_parser.add_subparsers(dest='db_command', help='数据库子命令')

    # 3. 二级子命令: db init
    init_parser = db_subparsers.add_parser('init', help='初始化数据库')
    init_parser.add_argument('--name', required=True, help='数据库名称')
    init_parser.set_defaults(func=handle_db_init)

    # 4. 二级子命令: db backup
    backup_parser = db_subparsers.add_parser('backup', help='备份数据库')
    backup_parser.add_argument('--output', '-o', help='输出路径')
    backup_parser.set_defaults(func=handle_db_backup)

    # 5. 一级子命令: file
    file_parser = subparsers.add_parser('file', help='文件操作')
    file_parser.add_argument('path', help='文件路径')
    file_parser.set_defaults(func=handle_file)

    # 解析参数并执行
    args = parser.parse_args()
    if hasattr(args, 'func'):
        args.func(args)  # 调用关联的函数
    else:
        parser.print_help()  # 无命令时显示帮助

# 命令处理函数
def handle_db_init(args):
    print(f"初始化数据库: name={args.name}")

def handle_db_backup(args):
    print(f"备份数据库: output={args.output or '默认路径'}")

def handle_file(args):
    print(f"处理文件: path={args.path}")

if __name__ == '__main__':
    main()