# import subprocess
# import argparse
# import sys

# def run_command(command, description):
#     """执行命令并处理错误"""
#     print(f">>> 执行命令: {command}")
#     result = subprocess.run(command, shell=True)
#     if result.returncode != 0:
#         print(f"!!! 错误: {description}失败")
#         sys.exit(1)
#     print(f"✓ {description}成功\n")

# def main():
#     # 设置命令行参数
#     parser = argparse.ArgumentParser(description='Docker镜像构建与推送工具')
#     parser.add_argument('--build-tag', required=True, help='构建镜像的标签 (e.g. deepseek-ai/deepseek-r1-distill-qwen-32b:biren)')
#     parser.add_argument('--new-tag', required=True, help='新的镜像标签 (e.g. **********:17432/moments8/deepseek-ai/deepseek-r1-distill-qwen-32b:biren)')
#     args = parser.parse_args()

#     print("\n" + "="*50)
#     print(f"开始处理镜像: {args.build_tag}")
#     print("="*50)

#     # 步骤1: 构建Docker镜像
#     build_cmd = f"docker build -t {args.build_tag} ."
#     run_command(build_cmd, "Docker镜像构建")

#     # 步骤2: 添加新标签
#     tag_cmd = f"docker tag {args.build_tag} {args.new_tag}"
#     run_command(tag_cmd, "添加镜像标签")

#     # 步骤3: 推送镜像到仓库
#     push_cmd = f"docker push {args.new_tag}"
#     run_command(push_cmd, "推送镜像到仓库")

#     print("="*50)
#     print(f"✓ 所有操作完成! 镜像已推送到: {args.new_tag}")
#     print("="*50)

# if __name__ == "__main__":
#     main()

import subprocess

cmd = [
    'sudo', 'regctl', 'manifest', 'get', 
    "**********:17432/moments8/deepseek-r1-distill-qwen-7b:ximu",
    '--format', 
    '{{ .GetDescriptor.Digest }} {{ .GetDescriptor.Size }}'
]

result = subprocess.run(
            cmd,
            capture_output=True,
            text=True
        )

print(result.returncode)
digest, size = result.stdout.strip().split()

print(digest, size)