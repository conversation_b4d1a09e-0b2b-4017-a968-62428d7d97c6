import argparse
import json
import os
import sys
import math

def get_model_config(model_config_json, model_safetensors_json, gpu_num):

    try:
        with open(model_safetensors_json, 'r', encoding='utf-8') as file:
            safetensors = json.load(file)
    except Exception as e:
        print(f"Read {model_safetensors_json} error, {e}")
        sys.exit(1)
    
    total_size = safetensors["metadata"]["total_size"]

    if total_size / 1e9 < 18:
        print("The model just need one card!")
        return 1,1
    
    try:
        with open(model_config_json, 'r', encoding='utf-8') as file:
            config = json.load(file)
    except Exception as e:
        print(f"Read {model_config_json} error, {e}")
        sys.exit(1)

    tp_config_keys = ["hidden_size", "num_attention_heads", "intermediate_size", "vocab_size"]

    tp_config_values = []

    if all(key in config for key in tp_config_keys):
        for key in tp_config_keys:
            tp_config_values.append(config[key])
    else:
        print("Not support TP!")
        return 1,gpu_num
    
    if "num_key_value_heads" in config:
        tp_config_values.append(config["num_key_value_heads"])
    
    return tp_config_values, total_size


def check_parameter(gpu_num, concurrency_num, concurrency_num_per_pp, min_gpu_num):
    if concurrency_num <= concurrency_num_per_pp * gpu_num and min_gpu_num <= gpu_num:
        print("Set parameters reasonably!")
    else:
        print("The setting parameters are unreasonable, please reset them!")
        sys.exit(1)


def cal_min_gpu(total_size, gpu_memory_per):
    min_gpu_memory = total_size / 1e9 * 1.5 * 1.2
    print("min_gpu_memory: ",min_gpu_memory)
    min_gpu_num = math.ceil(min_gpu_memory / gpu_memory_per)
    if min_gpu_num != 1:
        min_gpu_num = math.ceil(min_gpu_num) + 1 if math.ceil(min_gpu_num) % 2 != 0 else math.ceil(min_gpu_num)

    return min_gpu_num


def cal_tp_pp_value(config_list: list, gpu_num: int, concurrency_num: int, concurrency_num_per_pp: int) -> tuple:
    tp_value_list = []
    for i in range(gpu_num):
        if all(isinstance(p, int) and p % (i + 1) == 0 for p in config_list):
            tp_value_list.append(i + 1)
    
    pp_value_list = list(range(math.ceil(concurrency_num / concurrency_num_per_pp), gpu_num + 1))

    print(tp_value_list)
    print(pp_value_list)

    tp_pp_group_list = []
    for tp in tp_value_list:
        for pp in pp_value_list:
            if tp * pp == gpu_num:
                tp_pp_group_list.append([tp, pp])
    print(tp_pp_group_list)

    min_pair = min(tp_pp_group_list, key=lambda x: abs(x[0] - x[1]))

    print("min_pair: ", min_pair)

    return min_pair


def main():
    parser = argparse.ArgumentParser(description="pp tp set")
    parser.add_argument("--model_id", default="deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B", 
                        help="Hugging face model")
    parser.add_argument("--model_path", default="./7b", 
                        help="Model path")
    parser.add_argument("--gpu_num", type=int, default=2, 
                        help="Gpu number: 1~16")
    parser.add_argument("--concurrency_num", type=int, default=32, 
                        help="Request concurrency: 32~128")
    parser.add_argument("--concurrency_num_per_pp", type=int, default=32, 
                        help="The maximum concurrency supported by a PP")
    parser.add_argument("--gpu_memory_per", type=int, default=32, 
                        help="Memory per gpu")


    args = parser.parse_args()


    model_config_json = os.path.join(args.model_path, "config.json")
    model_safetensors_json = os.path.join(args.model_path, "model.safetensors.index.json")

    # step 0: Determine special circumstances
    config_list, total_size = get_model_config(model_config_json, model_safetensors_json, args.gpu_num)

    if type(config_list) is not list:
        print(f"TP is {config_list}, PP is {total_size}!")
        sys.exit(1) 


    # step 1: Calculate the minimum number of GPUs required
    min_gpu_num = cal_min_gpu(total_size, args.gpu_memory_per)
    print("min_gpu_num: ", min_gpu_num)

    # step 2: Check the validity of parameter settings
    check_parameter(args.gpu_num, args.concurrency_num, args.concurrency_num_per_pp, min_gpu_num)

    # step 3: Calculate the optimal values ​​of tp and pp
    tp_pp_pair = cal_tp_pp_value(config_list, args.gpu_num, args.concurrency_num, args.concurrency_num_per_pp)

    print("tp is: ", tp_pp_pair[0])
    print("pp is: ", tp_pp_pair[1])


if __name__ == "__main__":
    main()