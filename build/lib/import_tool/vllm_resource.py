import os
import json
import math
from typing import Tu<PERSON>, Optional, Dict, Any


class VLLMResourceCalculator:
    def __init__(self, model_path):
        """
        Initialize Resource Calculator
        parameter: Hugging Face model path (local directory)
        """
        self.model_path = model_path
        self.config = self._load_config()
        self.safetensors = self._get_safetensors()
    
    def _load_config(self) -> Dict[str, Any]:
        config_path = os.path.join(self.model_path, 'config.json')
        if not os.path.exists(config_path):
            print(f"The configuration file does not exist: {config_path}")
            # raise FileNotFoundError(f"The configuration file does not exist: {config_path}")
        with open(config_path, 'r') as f:
            config = json.load(f)
        return config

    def _get_safetensors(self) -> int:
        safetensors_path = os.path.join(self.model_path, 'model.safetensors.index.json')
        if not os.path.exists(safetensors_path):
            # raise FileNotFoundError(f"The safetensors file does not exist: {safetensors_path}")
            return 3527752704
        with open(safetensors_path, 'r') as f:
            safetensors = json.load(f)
        return safetensors["metadata"]["total_size"]
    
    def min_gpu_required(self, mem_per_gpu) -> int:
        min_gpu_memory = self.safetensors / 1e9 * 1.5 * 1.2
        min_gpu_num = math.ceil(min_gpu_memory / mem_per_gpu)
        if min_gpu_num != 1:
            min_gpu_num = math.ceil(min_gpu_num) + 1 if math.ceil(min_gpu_num) % 2 != 0 else math.ceil(min_gpu_num)

        return min_gpu_num
    
    def get_valid_tp_values(self, mem_per_gpu) -> list:
        tp_config_keys = ["hidden_size", "num_attention_heads", "intermediate_size", "vocab_size"]

        tp_config_values = []
        if all(key in self.config for key in tp_config_keys):
            for key in tp_config_keys:
                tp_config_values.append(self.config[key])
        else:
            return [1]
        
        if "num_key_value_heads" in self.config:
            tp_config_values.append(self.config["num_key_value_heads"])

        tp_value_list = []

        for i in range(self.min_gpu_required(mem_per_gpu)):
            if all(isinstance(p, int) and p % (i + 1) == 0 for p in tp_config_values):
                tp_value_list.append(i + 1)
        
        return tp_value_list
    
    def get_tp_pp_values(self, mem_per_gpu, concurrency_num, concurrency_num_per_pp) -> list:
        min_gpu_num = self.min_gpu_required(mem_per_gpu)
        tp_value_list = self.get_valid_tp_values(mem_per_gpu)

        pp_value_list = list(range(math.ceil(concurrency_num / concurrency_num_per_pp), min_gpu_num + 1))

        tp_pp_group_list = []
        for tp in tp_value_list:
            for pp in pp_value_list:
                if tp * pp == min_gpu_num:
                    tp_pp_group_list.append([tp, pp])
        
        min_pair = min(tp_pp_group_list, key=lambda x: abs(x[0] - x[1]))

        return min_pair


if __name__ == "__main__":
    calculator = VLLMResourceCalculator("/home/<USER>/wenfeng.zhang/import-tool/test/model")

    gpu_mem = 32

    concurrency_num = 32 # 并发量
    concurrency_num_per_pp = 32

    min_gpu_num = calculator.min_gpu_required(gpu_mem)
    print(min_gpu_num)

    tp_value_list = calculator.get_valid_tp_values(gpu_mem)
    print(tp_value_list)    

    tp_pp_pair = calculator.get_tp_pp_values(gpu_mem, concurrency_num, concurrency_num_per_pp)

    print(tp_pp_pair)
    print(type(tp_pp_pair))