import os
import sys
from modelscope.hub.snapshot_download import snapshot_download

def download_model(model_id, model_path):
    if os.path.exists(model_path):
        print("The model path exist, not need download!")
        return
    else:
        os.makedirs(model_path, exist_ok=True)
    
    model_path = snapshot_download(
    model_id=model_id,
    local_dir=model_path,
    revision='master',  # master branch
)   
    required_files = [
    'config.json',           # config file
    'tokenizer.json'
    ]
    missing = [f for f in required_files if not os.path.exists(os.path.join(model_path, f))]
    
    if not missing:
        print("✅ Key documents are complete! ")
    else:
        print(f"❌ Miss file: {missing}")
    
    print(f"{model_id} model download complete!")