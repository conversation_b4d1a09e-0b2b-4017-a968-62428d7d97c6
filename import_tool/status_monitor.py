import json
import time
from datetime import datetime, timezone

STATUS_FILE = "llm_import_status.json"

# STATUS_FILE = "/home/<USER>/wenfeng.zhang/model-import-utility/test/llm_import_status.json"

def current_utc_time():
    """返回当前UTC时间的字符串格式"""
    return datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%SZ")

def calcute_duration(time_str1, time_str2):
    dt1 = datetime.strptime(time_str1, "%Y-%m-%dT%H:%M:%SZ")
    dt2 = datetime.strptime(time_str2, "%Y-%m-%dT%H:%M:%SZ")

    # 计算时间差
    delta = dt2 - dt1

    # 获取总秒数
    duration = delta.total_seconds()
    return duration

def show_state(json_file):
    with open(json_file, 'r') as f:
        state_file = json.load(f)
    state_file["pipeline"]["updated_at"] = current_utc_time()
    step_list = [state_file["pipeline"]["steps"][i]["status"] for i in range(3)]
    if "failure" in step_list:
        state_file["pipeline"]["status"] = "failure"
        # index = step_list.index("failure")
        state_file["pipeline"]["error"] = True
    elif all(x == "success" for x in step_list):
        state_file["pipeline"]["status"] = "success"
        state_file["pipeline"]["finished_at"] = state_file["pipeline"]["steps"][2]["finished_at"]
        state_file["pipeline"]["duration"] = calcute_duration(state_file["pipeline"]["started_at"], state_file["pipeline"]["finished_at"])
        state_file["pipeline"]["progress"]["completed_steps"] = 3
        state_file["pipeline"]["progress"]["percentage"] = 100
    else:
        state_file["pipeline"]["duration"] = calcute_duration(state_file["pipeline"]["started_at"], state_file["pipeline"]["updated_at"])
        if step_list[1] == "pending" and step_list[0] == "success":
            state_file["pipeline"]["progress"]["completed_steps"] = 1
            state_file["pipeline"]["progress"]["percentage"] = 33
        if step_list[2] == "pending" and step_list[0] == "success" and step_list[1] == "success":
            state_file["pipeline"]["progress"]["completed_steps"] = 2
            state_file["pipeline"]["progress"]["percentage"] = 66

    # print(json.dumps(state_file, indent=2))
    return json.dumps(state_file)

def init_status(remote_image):
    """初始化状态数据结构"""
    timestamp = datetime.utcnow().strftime("%Y-%m-%dT%H:%M:%SZ")
    job_id = f"import-model-job-{int(time.time())}"
    
    return {
        "pipeline": {
            "id": job_id,
            "name": "import-model-job",
            "status": "running",
            "created_at": timestamp,
            "started_at": timestamp,
            "updated_at": timestamp,
            "finished_at": None,
            "duration": 0,
            "error": None,
            "generated_image": remote_image,
            "progress": {
                "completed_steps": 0,
                "total_steps": 3,
                "percentage": 0
            },
            "steps": [
                {
                    "id": 0,
                    "name": "Download Model",
                    "status": "pending",
                    "started_at": None,
                    "finished_at": None,
                    "duration": 0,
                    "exit_code": None,
                    "error_infor": None
                },
                {
                    "id": 1,
                    "name": "Build Images",
                    "status": "pending",
                    "started_at": None,
                    "finished_at": None,
                    "duration": 0,
                    "exit_code": None,
                    "error_infor": None
                },
                {
                    "id": 2,
                    "name": "Upload Images",
                    "status": "pending",
                    "started_at": None,
                    "finished_at": None,
                    "duration": 0,
                    "exit_code": None,
                    "error_infor": None
                }
            ]
        }
    }

def update_status_file(status, json_file):
    """更新状态文件"""
    with open(json_file, 'w') as f:
        json.dump(status, f, indent=2)

if __name__ == "__main__":
    json_file = "/home/<USER>/wenfeng.zhang/model-import-utility/test/llm_import_status.json"
    # show_state(json_file)

    # time_str1 = current_utc_time()
    # print(type(time_str1))
    # print(time_str1)

    # time.sleep(2)

    # time_str2 = current_utc_time()
    # print(time_str2)

    # duration_time = calcute_duration(time_str1, time_str2)

    # print(duration_time)
    # print(type(duration_time))

    state_file = show_state(json_file)

