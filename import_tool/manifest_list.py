import json
import subprocess

def get_digests_size(image_name_list):
    if len(image_name_list) == 0:
        raise ValueError("Docker image is none!")
    digest_list = []
    size_list = []
    for image in image_name_list:
        cmd = [
            'regctl', 'manifest', 'get', 
            image, 
            '--format', 
            '{{ .GetDescriptor.Digest }} {{ .GetDescriptor.Size }}'
        ]
        result = subprocess.run(
                cmd,
                capture_output=True,
                text=True
            )
        if result.returncode != 0:
            print(result.stderr)
            raise ValueError("Get manifest digest and size infor error!")
        digest, size = result.stdout.strip().split()
        digest_list.append(digest)
        size_list.append(size)
    return digest_list, size_list
    

def create_manifest_list(output_path, num, image_name_list, index):

    digest_list, size_list = get_digests_size(image_name_list)

    # default_digests = [
    #     "sha256:087d4ce3ce16c2c4b78806d70782443fe48d438c9ee70b75efc1cfbc282a5f23",
    #     "sha256:90f40df815ff2a5697f8d3c864520b375b984de9974b3a6427ec81d138ef86da"
    # ]
    # default_sizes = [7625, 2004]
    
    # digests = digests or default_digests[:num]
    # sizes = sizes or default_sizes[:num]

    if len(digest_list) < num or len(size_list) < num:
        raise ValueError(f"digests/sizes must have at least {num} elements")
    
    manifest_templates = [
        {
            "mediaType": "application/vnd.oci.image.manifest.v1+json",
            "platform": {
                "architecture": "amd64",
                "os": "linux",
                "os.features": ["birentech.com/gpu", "106C"]
            },
            "annotations": {
                "moments8/torin": json.dumps({
                    "request": {"birentech.com/gpu": "1", "cpu": "32", "memory": "128Gi"},
                    "limit": {"birentech.com/gpu": "1", "cpu": "32", "memory": "128Gi"},
                    "tps": "32",
                    "nodeCapacity": "16",
                    "step": "2"
                })
            }
        },
        {
            "mediaType": "application/vnd.oci.image.manifest.v1+json",
            "platform": {
                "architecture": "amd64",
                "os": "linux",
                "os.features": ["streamcomputing.com/npu", "STCP920"]
            },
            "annotations": {
                "moments8/torin": json.dumps({
                    "request": {"streamcomputing.com/npu": "4", "cpu": "32", "memory": "128Gi"},
                    "limit": {"streamcomputing.com/npu": "4", "cpu": "32", "memory": "128Gi"},
                    "tps": "32",
                    "nodeCapacity": "16",
                    "step": "0"
                })
            }
        }
    ]

    manifests = []
    if num == 1:
        manifest = manifest_templates[index].copy()
        manifest.update({
            "digest": digest_list[0],
            "size": int(size_list[0])
        })
        manifests.append(manifest)
    else:
        for i in range(num):
            manifest = manifest_templates[i].copy()
            manifest.update({
                "digest": digest_list[i],
                "size": int(size_list[i])
            })
            manifests.append(manifest)
    
    oci_index = {
        "schemaVersion": 2,
        "mediaType": "application/vnd.oci.image.index.v1+json",
        "manifests": manifests,
        "annotations": {
            "moments8/torin": json.dumps({
                "request": {"moments8.com/gpu": "2", "cpu": "32", "memory": "128Gi"},
                "limit": {"moments8.com/gpu": "2", "cpu": "32", "memory": "512Gi"},
                "tps": 32,
                "modelDescription": "DeepSeek-R1-Distill-Qwen-7B是通过知识蒸馏技术从DeepSeek-R1模型中提炼出来的模型。它继承了DeepSeek-R1的推理能力，在数学、编程和推理等多个领域展现出卓越的性能。"
            })
        }
    }
    
    # 写入JSON文件
    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(oci_index, f, indent=4, ensure_ascii=False)
    
    print(f"JSON文件已生成至: {output_path}")

def put_manifest_list(remote_image, json_file):
    cmd = [
        'regctl', 'manifest', 'put', 
        remote_image
    ]

    with open(json_file, 'r') as f:
        result = subprocess.run(
            cmd,
            stdin=f,
            text=True,
            capture_output=True
        )
    if result.returncode != 0:
        print(result.stderr)
        raise ValueError("Get manifest digest and size infor error!")
    print("Put manifest_list success!")

if __name__ == "__main__":
    # 示例1：生成包含2个manifest的文件（默认）
    num = 2
    image_name_list = ["172.16.7.9:17432/moments8/deepseek-r1-distill-qwen-7b:biren",
                       "172.16.7.9:17432/moments8/deepseek-r1-distill-qwen-7b:ximu"]
    index = 0
    create_manifest_list("manifest_list.json", num, image_name_list, index)