import argparse
import os
import sys

from .utils import judge_remote_image

from .download_model import download_model

from .vllm_resource import VLLMResourceCalculator

from .biren_model_run import biren_model_run

from .create_dockerfile import create_biren_dockerfile, create_ximu_dockerfile

from .build_tag_push_image import build_image, tag_image, push_image

from .ximu_weights_convert import convert_weights, MODEL_DICT

from .manifest_list import create_manifest_list, put_manifest_list

from .status_monitor import STATUS_FILE, show_state, init_status, update_status_file, current_utc_time, calcute_duration


def main():
    parser = argparse.ArgumentParser(description="import tools")
    parser.add_argument("--model_id", "--import", default="deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B", 
                        help="hugging face model")
    parser.add_argument("--model_path", default="./model", 
                        help="download model path")
    
    parser.add_argument("--remote_address", default="harbor.intra.moments8.com/moments8/torin/", 
                        help="remote address")
    
    # biren gpu param
    parser.add_argument("--biren_gpu_mem", type=int, default=32, 
                        help="biren gpu memory")
    parser.add_argument("--biren_concurrency_num", type=int, default=32, 
                        help="biren oncurrency num")
    parser.add_argument("--biren_concurrency_num_per_pp", type=int, default=32, 
                        help="biren oncurrency num per pp")
    
    # biren dockerfile param
    parser.add_argument("--biren_base_image", default="harbor.moments8.local:17432/moments8/base_image/biren/birensupa-vllm:25.05.16-py310-pt2.3.0-c024s001t001b15652-br1xx", 
                        help="biren base image")
    
    # ximu weights convert
    parser.add_argument("--venv_python", default="/workspace/ximu_convert/venv/bin/python3", 
                        help="hugging face model")
    parser.add_argument("--ximu_weights_path", default="./ximu", 
                        help="hugging face model")
    
    parser.add_argument("--ximu_base_image", default="harbor.moments8.local:17432/moments8/base_image/ximu/ximu_sdk_hpe:v1.1.0", 
                        help="ximu base image")
    
    parser.add_argument("--status", action='store_true')

    parser.add_argument("--tag_mark", default="_test_01", 
                        help="image name mark")

    args = parser.parse_args()

    if args.status:
        if not os.path.exists(STATUS_FILE):
            print("Not import model")
            return False
        state_file = show_state(STATUS_FILE)
        return state_file

    # Does the remote mirror exist
    remote_image = (args.remote_address + args.model_id.split('/')[0] + "_" + args.model_id.split('/')[-1] + args.tag_mark + ":v1.0").lower()

    biren_local_iamge = (args.model_id.split('/')[0] + "_" + args.model_id.split('/')[-1] + args.tag_mark + ":biren").lower()
    biren_remote_image = args.remote_address + biren_local_iamge

    ximu_local_iamge = (args.model_id.split('/')[0] + "_" + args.model_id.split('/')[-1] + args.tag_mark + ":ximu").lower()
    ximu_remote_image = args.remote_address + ximu_local_iamge

    # init state
    status = init_status(remote_image)
    update_status_file(status=status, json_file=STATUS_FILE)

    # Determine whether the remote repository has the model image
    try:
        judge_remote_image(remote_image)
    except Exception as e:
        print(f"Error: {e}")
        status["pipeline"]["status"] = "success"
        update_status_file(status=status, json_file=STATUS_FILE)
        sys.exit(1)
    except ValueError as e:
        print(f"Error: {e}")
        status["pipeline"]["status"] = "success"
        print(f"Error: {e}")
        sys.exit(1)
    
    # step 1: Download mdoel!
    step_0 = status["pipeline"]["steps"][0]
    step_0["status"] = "running"
    step_0["started_at"] = current_utc_time()
    update_status_file(status=status, json_file=STATUS_FILE)

    print("Step1: Download {} model to {}.".format(args.model_id, args.model_path))

    try:
        download_model(args.model_id, args.model_path)
        step_0["status"] = "success"
        step_0["finished_at"] = current_utc_time()
        step_0["duration"] = calcute_duration(step_0["started_at"], step_0["finished_at"])
        update_status_file(status=status, json_file=STATUS_FILE)
    except Exception as e:
        step_0["status"] = "failure"
        step_0["exit_code"] = 1
        step_0["error_infor"] = str(e)
        update_status_file(status=status, json_file=STATUS_FILE)
        print(f"Download model error: {e}")
        sys.exit(1)
        
        
    # step 2: build image
    step_1 = status["pipeline"]["steps"][1]
    step_1["status"] = "running"
    step_1["started_at"] = current_utc_time()
    update_status_file(status=status, json_file=STATUS_FILE)

    print("Step 2: Build image!")

    manifest_num_list = []

    try:
        # 2.1: Build biren docker image
        # 2.1.1 get tp,pp value
        print("Calculate biren tp pp value")
        calculator = VLLMResourceCalculator(args.model_path)
        tp_pp_list = calculator.get_tp_pp_values(args.biren_gpu_mem, args.biren_concurrency_num, args.biren_concurrency_num_per_pp)
        tp_value = tp_pp_list[0]
        pp_value = tp_pp_list[1]
        print(f"Biren tp value: {tp_value}")
        print(f"Biren pp value: {pp_value}")

        # 2.1.2 run model in biren environment
        print("Determine whether the model can run on biren GPU")
        biren_state = biren_model_run(tp_value, pp_value, model_name=args.model_id.split('/')[-1], model_path=args.model_path)

        # biren_state = True
        if biren_state == True:
            print(f"The model {args.model_id} run in biren gpu success!")

            # 2.1.3 create biren dockerfile
            print("Create biren Dockerfile")
            dockerfile_biren = create_biren_dockerfile(base_image=args.biren_base_image,
                                                    model_name=args.model_id,
                                                    model_path=os.path.join("/workspace", args.model_id.split('/')[-1]),
                                                    pp_size=pp_value,
                                                    tp_size=tp_value)
            
            dockerfile = "Dockerfile"
            if os.path.exists(dockerfile):
                os.remove(dockerfile)

            with open(dockerfile, "w") as f:
                f.write(dockerfile_biren)

            print("Create biren dockerfile success!")

            # 2.1.4 build tag and push biren docker image
            build_image(local_image=biren_local_iamge)
            tag_image(local_image=biren_local_iamge, remote_image=biren_remote_image)

            manifest_num_list.append("biren")
        
        else:
            print(f"The model {args.model_id} run in biren gpu failed!")
    except Exception as e:
        print(f"Build biren image error: {e}")
    except ValueError as e:
        print(f"Build biren image error: {e}")
    
    
    try:
        # 2.2 Build ximu docker image
        # 2.2.1 convert ximu weights
        print("Convert ximu weights")
        ximu_state = convert_weights(args.venv_python, args.model_id, args.model_path, args.ximu_weights_path)
        # ximu_state = True
        if ximu_state == True:
            print("Convert ximu weights success")

            # 2.2.2 create ximu dockerfile
            print("Create ximu Dockerfile")
            ximu_num_npu = MODEL_DICT[args.model_id][0]
            dockerfile_ximu = create_ximu_dockerfile(base_image=args.ximu_base_image,
                                                    model_name=args.model_id,
                                                    model_path=os.path.join("/workspace", args.model_id.split('/')[-1]),
                                                    num_npu=ximu_num_npu)
            dockerfile = "Dockerfile"
            if os.path.exists(dockerfile):
                os.remove(dockerfile)

            with open(dockerfile, "w") as f:
                f.write(dockerfile_ximu)

            print("Create ximu Dockerfile success!")

            # 2.2.3 build tag and push ximu docker image
            build_image(local_image=ximu_local_iamge)
            tag_image(local_image=ximu_local_iamge, remote_image=ximu_remote_image)
            
            manifest_num_list.append("ximu")
            
        else:
            print(f"The model {args.model_id} run in ximu npu failed!")
    except Exception as e:
        print(f"Build ximu image error: {e}")
    except ValueError as e:
        print(f"Build ximu image error: {e}")


    if len(manifest_num_list) == 0:
        step_1["status"] = "failure"
        step_1["exit_code"] = 1
        step_1["error_infor"] = "Gpu not support this model!"
        update_status_file(status=status, json_file=STATUS_FILE)
        print("Gpu not support this model!")
        sys.exit(1)
    
    if len(manifest_num_list) != 0:
        step_1["status"] = "success"
        step_1["finished_at"] = current_utc_time()
        step_1["duration"] = calcute_duration(step_1["started_at"], step_1["finished_at"])
        update_status_file(status=status, json_file=STATUS_FILE)    

    
    # step3: push image and create manifest list
    step_2 = status["pipeline"]["steps"][2]
    step_2["status"] = "running"
    step_2["started_at"] = current_utc_time()
    update_status_file(status=status, json_file=STATUS_FILE)

    print("Step3: push image and create manifest list")

    try:
        if len(manifest_num_list) == 1 and manifest_num_list[0] == "biren":
            index = 0
            push_image(biren_remote_image)
            remote_image_list = [biren_remote_image]
            create_manifest_list(output_path="manifest_list.json", num=1,image_name_list=remote_image_list, index=index)

        if len(manifest_num_list) == 1 and manifest_num_list[0] == "ximu":
            index = 1
            push_image(ximu_remote_image)
            remote_image_list = [ximu_remote_image]
            create_manifest_list(output_path="manifest_list.json", num=1,image_name_list=remote_image_list, index=index)

        if len(manifest_num_list) == 2:
            push_image(biren_remote_image)
            push_image(ximu_remote_image)

            remote_image_list = [biren_remote_image, ximu_remote_image]
            create_manifest_list(output_path="manifest_list.json", num=2,image_name_list=remote_image_list, index=0)
        
        if os.path.exists("manifest_list.json"):
            print("Ready to push manifest_list!")
        else:
            print("manifest_list file not exists!")
        
        # push manifest list
        json_file = "manifest_list.json"
        put_manifest_list(remote_image=remote_image, json_file=json_file)
        
        step_2["status"] = "success"
        step_2["finished_at"] = current_utc_time()
        step_2["duration"] = calcute_duration(step_2["started_at"], step_2["finished_at"])
        update_status_file(status=status, json_file=STATUS_FILE)    

    except Exception as e:
        step_2["status"] = "failure"
        step_2["exit_code"] = 1
        step_2["error_infor"] = str(e)
        update_status_file(status=status, json_file=STATUS_FILE)
        sys.exit(1)

    except ValueError as e:
        step_2["status"] = "failure"
        step_2["exit_code"] = 1
        step_2["error_infor"] = str(e)
        update_status_file(status=status, json_file=STATUS_FILE)
        sys.exit(1)
    
    print("SUccess! Down!")

if __name__ == "__main__":
    main()