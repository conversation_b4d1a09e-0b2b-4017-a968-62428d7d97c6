import subprocess


def run_command(command, description):
    """execute commands and handle errors"""
    print(f">>> execute command: {command}")
    result = subprocess.run(command, shell=True)
    if result.returncode != 0:
        # print(f"!!! error: {description} failed")
        raise ValueError(f"!!! error: {description} failed")
    print(f"✓ {description} success!\n")
    

def build_image(local_image: str):
    
    build_cmd = f"docker build -t {local_image} ."
    return run_command(build_cmd, "Docker image build!")


def tag_image(
        local_image: str,
        remote_image: str):

    tag_cmd = f"docker tag {local_image} {remote_image}"
    return run_command(tag_cmd, "Docker image tag!")


def push_image(remote_image: str,):
    push_cmd = f"docker push {remote_image}"
    return run_command(push_cmd, "Docker image push!")