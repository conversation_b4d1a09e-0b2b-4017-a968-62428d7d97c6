import subprocess
import sys

def judge_remote_image(remote_image):
    cmd = [
        'regctl', 'manifest', 'get', 
        remote_image
    ]
    result = subprocess.run(
            cmd,
            capture_output=True,
            text=True
        )
    if result.returncode != 0:
        # print(result.stderr)
        print(f"Remote repository not have {remote_image}")
    else:
        # print(f"Remote repository have {remote_image}")
        raise ValueError(f"Remote repository have {remote_image}")


if __name__ == "__main__":
    # 示例1：生成包含2个manifest的文件（默认）
    
    remote_image = "172.16.7.9:17432/moments8/deepseek-r1-distill-qwen-7b:v1.11"
    judge_remote_image(remote_image)