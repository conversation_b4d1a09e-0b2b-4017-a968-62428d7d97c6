import subprocess
import time
import os
import signal

def biren_model_run(tp_value, pp_value, model_name, model_path):

    supa_devices = ','.join(str(i) for i in range(tp_value*pp_value))

    commands = [
        "source /usr/local/birensupa/brsw_set_env.sh",
        "export BRTB_ENABLE_NUMA_ALIGN_4K=1 && export BRTB_ENABLE_NUMA_SPLIT=1 && export VLLM_WORKER_MULTIPROC_METHOD=spawn",
        f"export SUPA_VISIBLE_DEVICES={supa_devices}",
        f"python3 -m vllm.entrypoints.openai.api_server --served-model-name {model_name} "
        f"--model {model_path} "
        "--device=supa --gpu_memory_utilization=0.8 --block_size=128 "
        "--port 25000 --dtype=bfloat16 --max-model-len 32768 "
        "--max-num-seqs 32 --max_num_batched_tokens 8192 "
        f"--pipeline-parallel-size {pp_value} --tensor-parallel-size {tp_value} "
        "--distributed-executor-backend=mp --enable-chunked-prefill --trust-remote-code"
    ]

    full_cmd = " && ".join(commands)

    process = subprocess.Popen(
        full_cmd,
        shell=True,
        executable="/bin/bash",  # 确保支持source
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        text=True,
        preexec_fn=os.setsid
    )

    time.sleep(60)

    if process.poll() is None:
        print("Task still running... PID=", process.pid)
        os.killpg(os.getpgid(process.pid), signal.SIGTERM)
        return True
    else:
        print("This model run failed in biren gpu!")
        return False

if __name__ == "__main__":
    tp_value = 1
    pp_value = 1
    model_name = "DeepSeek-R1-Distill-Qwen-1.5B"
    model_path = "/models/deepseek/models--deepseek-ai--DeepSeek-R1-Distill-Qwen-1.5B/snapshots/6393b7559e403fd1d80bfead361586fd6f630a4d"

    biren_state = biren_model_run(tp_value=tp_value, pp_value=pp_value, model_name=model_name, model_path=model_path)