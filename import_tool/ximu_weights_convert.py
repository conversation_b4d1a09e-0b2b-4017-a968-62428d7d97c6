import subprocess
import os

MODEL_DICT = {
    # huggingface model id mapping
    "baichuan-inc/Baichuan2-7B-Chat": [1,2,4,8],
    "baichuan-inc/Baichuan2-7B-Base": [1,2,4,8],
    "Tele-AI/TeleChat-12B-v2": [2,4,8],
    "THUDM/chatglm2-6b": [1,2,4,8],
    "THUDM/chatglm3-6b": [1,2,4,8],
    "THUDM/chatglm3-6b-base": [1,2,4,8],
    "THUDM/chatglm3-6b-32k": [1,2,4,8],
    "THUDM/glm-4-9b-chat": [2,4,8],
    "internlm/internlm2-chat-7b": [2,4,8],
    "internlm/internlm2-chat-20b": [4,8],
    "internlm/internlm2-chat-20b-sft": [4,8],
    "meta-llama/Llama-2-7b": [1,2,4,8],
    "meta-llama/Llama-2-13b": [2,4,8],
    "FlagAlpha/Llama2-Chinese-7b-Chat": [1,2,4,8],
    "meta-llama/Meta-Llama-3-8B": [2,4,8],
    "meta-llama/Meta-Llama-3-8B-Instruct": [2,4,8],
    "FlagAlpha/Llama3-Chinese-8B-Instruct": [2,4,8],
    "meta-llama/Meta-Llama-3-70B-Instruct": [16],
    "meta-llama/Meta-Llama-3.1-8B-Instruct": [2,4,8],
    "meta-llama/Meta-Llama-3.1-70B-Instruct": [16],
    "mistralai/Mixtral-8x7B-Instruct-v0.1": [8],
    "fnlp/moss-moon-003-sft": [4,8],
    "microsoft/phi-2": [1],
    "Qwen/Qwen-7B-Chat": [2,4,8],
    "Qwen/Qwen1.5-1.8B-Chat": [1,2,4,8],
    "Qwen/Qwen1.5-7B-Chat": [2,4,8],
    "Qwen/Qwen1.5-14B-Chat": [2,4,8],
    "Qwen/Qwen1.5-32B-Chat": [8],
    "Qwen/Qwen2-0.5B-Instruct": [1],
    "Qwen/Qwen2-1.5B-Instruct": [1],
    "Qwen/Qwen2-7B-Instruct": [2,4,8],
    "Qwen/Qwen2-57B-A14B-Instruct": [8],
    "Qwen/Qwen2-72B-Instruct": [16],
    "Qwen/Qwen2.5-0.5B-Instruct": [1,2,4,8],
    "Qwen/Qwen2.5-1.5B-Instruct": [1,2,4,8],
    "Qwen/Qwen2.5-Math-1.5B-Instruct": [1,2,4,8],
    "Qwen/Qwen2.5-Coder-1.5B-Instruct": [1,2,4,8],
    "Qwen/Qwen2.5-3B-Instruct": [1,2,4,8],
    "Qwen/Qwen2.5-7B-Instruct": [2,4,8],
    "Qwen/Qwen2.5-Math-7B-Instruct": [2,4,8],
    "Qwen/Qwen2.5-Coder-7B-Instruct": [2,4,8],
    "Qwen/Qwen2.5-14B-Instruct": [4,8],
    "Qwen/Qwen2.5-32B-Instruct": [4,8],
    "Qwen/Qwen2.5-Coder-32B-Instruct": [4,8],
    "Qwen/Qwen2.5-72B-Instruct": [16],
    "Qwen/Qwen2.5-Math-72B-Instruct": [16],
    "AIDC-AI/Marco-o1": [2,4,8],
    "Qwen/QwQ-32B-Preview": [4,8],
    "xverse/XVERSE-13B-Chat": [2,4,8],
    "01-ai/Yi-34B-Chat": [8],
    "01-ai/Yi-1.5-34B-Chat": [8],
    "OpenBMB/MiniCPM-1B-sft-bf16": [1,2,4],
    "OpenBMB/miniCPM-bf16": [1,2,4],
    "deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B": [1,2],
    "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B": [4],
    "deepseek-ai/DeepSeek-R1-Distill-Llama-8B": [4],
    "deepseek-ai/DeepSeek-R1-Distill-Qwen-14B": [4],
    "deepseek-ai/DeepSeek-R1-Distill-Qwen-32B": [8],
    "deepseek-ai/DeepSeek-R1-Distill-Llama-70B": [16],
}


def convert_weights(venv_python, model_name, weight_path, output_path):
    if os.path.exists(output_path):
        print(f"The {output_path} exist!")
    else:
        os.makedirs(output_path, exist_ok=True)

    if model_name in MODEL_DICT:
        print(f"The {model_name} is supported by ximu npu!")
        npu_num = MODEL_DICT[model_name][0]
        command = [
            venv_python,
            "-m", "stc_llm_dnn.tools.convert_weight",
            "-m", model_name,
            "-p", weight_path,
            "-o", output_path,
            "-n", str(npu_num)
        ]

        result = subprocess.run(command, capture_output=True, text=True)

        stderr_lines = [line.strip() for line in result.stderr.splitlines() if line.strip()]

        if result.returncode != 0:
            print(f"Ximu weights convert failed! Return code: {result.returncode}")
            print(f"Error information: {stderr_lines[-1]}")
            return False
        else:
            print("Ximu weights convert success!")
            return True
    
    else:
        print(f"The {model_name} is supported by ximu npu!")
        return False   

if __name__ == "__main__":
    venv_python = "/home/<USER>/ximu_weights_convert/bin/python3"
    model_name = "deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B"
    weight_path = "/home/<USER>/import-tool_ximu_convert/DeepSeek-R1-Distill-Qwen-1.5B"
    output_path = "/home/<USER>/import-tool_ximu_convert/ximu_weights"

    statue_ximu = convert_weights(venv_python=venv_python, model_name=model_name, weight_path=weight_path, output_path=output_path)