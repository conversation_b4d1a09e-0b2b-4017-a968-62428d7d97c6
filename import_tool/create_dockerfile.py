
def create_biren_dockerfile(
        base_image: str,
        model_name: str,
        model_path: str,
        pp_size: int = 1,
        tp_size: int = 1
) -> str:
    
    supa_devices = ','.join(str(i) for i in range(tp_size*pp_size))

    dockerfile_content = f"""
FROM {base_image}
RUN mkdir -p {model_path}
COPY ./model {model_path}

ENTRYPOINT ["sh", "-c", "source /usr/local/birensupa/brsw_set_env.sh \\
    && export BRTB_ENABLE_NUMA_ALIGN_4K=1 \\
    && export BRTB_ENABLE_NUMA_SPLIT=1 \\
    && export VLLM_WORKER_MULTIPROC_METHOD=spawn \\
    && export SUPA_VISIBLE_DEVICES={supa_devices} \\
    && python3 -m vllm.entrypoints.openai.api_server \\
        --served-model-name {model_name} \\
        --model {model_path} \\
        --device=supa \\
        --gpu_memory_utilization=0.8 \\
        --block-size=128 \\
        --port 25000 \\
        --dtype=bfloat16 \\
        --max-model-len 32768 \\
        --max-num-seqs 32 \\
        --max-num-batched-tokens 8192 \\
        --pipeline-parallel-size {pp_size} \\
        --tensor-parallel-size {tp_size} \\
        --distributed-executor-backend=mp \\
        --enable-chunked-prefill \\
        --trust-remote-code"]

"""
    return dockerfile_content


def create_ximu_dockerfile(
        base_image: str,
        model_name: str,
        model_path: str,
        num_npu: int
) -> str:
    
    device = ','.join(str(i) for i in range(num_npu))

    dockerfile_content = f"""
FROM {base_image}
RUN mkdir -p {model_path}
COPY ./model/*json {model_path}/
COPY ./ximu {model_path}/

ENTRYPOINT ["sh", "-c", "export RISCV=/usr/local/hpe \\
    && python3 -m stc_llm.entrypoints.openai.api_server_langchain \\
        --model_name {model_name} \\
        --tok_dir {model_path} \\
        --original_weight_dir {model_path} \\
        --weight_dir {model_path} \\
        --max_tasks 256 \\
        --custom_parameters \\
        --temperature 0.7 \\
        --top_k 20 \\
        --max_output_len 4096 \\
        --port 18080 \\
        --device {device}"]

"""
    return dockerfile_content


if __name__ == "__main__":
    # dockerfile = create_biren_dockerfile(
    #     base_image="birensupa-vllm:25.05.16-py310-pt2.3.0-c024s001t001b15652-br1xx",
    #     model_name="deepseek-ai/DeepSeek-R1-Distill-Qwen-7B",
    #     model_path="/workspace/DeepSeek-R1-Distill-Qwen-7B",
    #     pp_size=1,
    #     tp_size=1
    # )

    # with open("Dockerfile", "w") as f:
    #     f.write(dockerfile)

    # print("Dockerfile generated successfully!")
    dockerfile = create_ximu_dockerfile(
        base_image="ximu_sdk:v1.1.0",
        model_name="deepseek-ai/DeepSeek-R1-Distill-Qwen-7B",
        model_path="/workspace/DeepSeek-R1-Distill-Qwen-7B",
        num_npu=4
    )

    with open("Dockerfile", "w") as f:
        f.write(dockerfile)

    print("Dockerfile generated successfully!")
